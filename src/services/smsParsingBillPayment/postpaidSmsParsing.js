import CATALOGVERTICALRECHARGE from '../../models/catalogVerticalRecharge';
import BILLS from '../../models/bills';
import recentBillLibrary from '../../lib/recentBills';
import utility from '../../lib';
import MOMENT from 'moment';
import <PERSON>YNC from 'async';
import RecentsLayerLib from '../../lib/recentsLayer';
import _ from 'lodash';
import VALIDATOR from 'validator';
import OS from 'os';
import BILLS_SUBSCRIBER from './../billSubscriber';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import DynamicSmsParsingRegexExecutor from './dynamicSmsParsingRegexExecutor'
import Q from 'q'
import service from 'recharge-config/service';
import operator from 'recharge-config/operator';
import BillFetchAnalytics from '../../lib/billFetchAnalytics'
import BillPush from '../../lib/billPush';
import PrepaidFlowManager from '../../lib/prepaidFlowManager';
import BillsLibrary from '../../lib/bills'
import InternalCustIdNonRUFlowTagger from '../../lib/InternalCustIdNonRUFlowTagger';

class postpaidSmsParsing {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], ['credit card']);
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.greyScaleEnv = options.greyScaleEnv;
        this.rechargeNumberAlreadySeen = []
        this.realtimePayloadIngestionTable = "sms_parsing_payload_ingestion"
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.smsParsingBillsDwhRealtime = _.get(options, 'smsParsingBillsDwhRealtime', false);
        this.regexExecutor = new DynamicSmsParsingRegexExecutor(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.BillPush = new BillPush(options);
        this.prepaidFlowManager = new PrepaidFlowManager(options);
        this.saveForAnalyticsInCassandraDbAndKafka = options.saveForAnalyticsInCassandraAndKafka ? true : false;
        this.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        this.billPushOperatorRegistrationAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_OPERATOR_REGISTRATION'], ['electricity']);
        this.BlockSmsOperatorForBillPush = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_BLOCK_OPERATOR_REGISTRATION'], ['uttar pradesh power corporation ltd. (uppcl)']);
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.minPrepaidBalanceCheck = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'MIN_BALANCE', 'AMOUNT'], 2000);
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], []);
        this.billsLib = new BillsLibrary(options);
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return _.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH';
    }

    initializeVariable(){
        this.L.verbose("Reinitializing variables")
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        this.billPushOperatorRegistrationAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_OPERATOR_REGISTRATION'], ['electricity']);
        this.BlockSmsOperatorForBillPush = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_BLOCK_OPERATOR_REGISTRATION'], ['uttar pradesh power corporation ltd. (uppcl)']);
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.minPrepaidBalanceCheck = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'MIN_BALANCE', 'AMOUNT'], 2000);

        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], []);
    }

    executeStrategy(done, record, ref) {
        let self = this;
        this.parent = ref;
        self.timestamps = {};
        self.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA

        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('1. executeStrategy:: start executing on postpaid parsed sms');

        try {
            if (!record) {
                self.L.log(`executeStrategy:: null smsData`);
                return done();
            }
            self.processRecordForDuplicateCANUmber(record, category);
            self.processRecord(record, function (err) {
                if (err) {
                    self.L.error(`SMS_PARSING_POSTPAID :: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                        `SERVICE:${category}`, 
                        'STATUS:PROCESSING_ERROR', 
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ])
                }
                return done();
            });
        }
        catch (err) {
            self.L.error(`SMS_PARSING_POSTPAID :: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                'STATUS:PROCESSING_ERROR', 
                'SOURCE:POSTPAID_SMS',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            return done();
        }

    }

    processRecordForDuplicateCANUmber(record, category) {
        let self = this;
        if(_.get(self.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR', _.toLower(record.operator), 'PREFIX'], "N.A") != "N.A"){
            let prefix = _.get(self.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR', _.toLower(record.operator), 'PREFIX'], "N.A");
            _.set(record, 'isDuplicateCANumberOperator', true);
            let clonedRecord = _.cloneDeep(record);
            _.set(record, 'alternateRechargeNumber', record.rechargeNumber.startsWith(prefix) ? record.rechargeNumber.substring(prefix.length) : prefix + record.rechargeNumber);
            _.set(clonedRecord, 'rechargeNumber', record.alternateRechargeNumber);
            _.set(clonedRecord, 'alternateCAExistsInDB', true);

            self.processRecord(clonedRecord, function (err) {
                if (err) {
                    self.L.error(`SMS_PARSING_POSTPAID :: Error for record :${JSON.stringify(clonedRecord)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                        `SERVICE:${category}`, 
                        'STATUS:PROCESSING_ERROR', 
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(clonedRecord)}`,
                        `APP_VERSION:${_.get(clonedRecord,'appVersion', null)}`
                    ])
                }
            });
        }
    }

    async ingestIncomingPayloads(errorResponse, processedRecord, record){
        let self=this;
        let due_date =_.get(record, 'telecom_details.due_date', _.get(record, 'dueDate',null));
        if(due_date){
            due_date = MOMENT(due_date).format('YYYY-MM-DD HH:mm:ss')
        }
        let ruOnboarded = _.get(processedRecord, 'ruOnboarded', false);
        let source_kafka_topic = _.get(self.config.KAFKA, 'SERVICES.REALTIME_SMS_PARSING_POSTPAID.TOPIC');
        let dbRecord = {
            "recharge_number" : _.get(record, 'rechargeNumber',null),
            "customer_id" : (typeof _.get(record,'cId',null) === 'number') ? _.get(record,'cId',null) : (typeof _.get(record,'cId',null) === 'string' && VALIDATOR.isNumeric(_.get(record,'cId',null))) ? VALIDATOR.toInt(_.get(record,'cId',null)) : null,
            "operator" : _.toLower( _.get(record, 'telecom_details.operator', _.get(record, 'operator',null))),
            "product_id" : _.get(processedRecord, 'productId', null),
            "service": _.get(processedRecord, 'service', null),
            "paytype": _.get(processedRecord, 'paytype', null),
            "classifier_id" : _.get(record, 'rtspClassId', null),
            "classifier_name" : _.get(record, 'rtspClassName', null),
            "template_body" : _.get(record, 'templateBody', null),
            "status" : (errorResponse)? 0:1,
            "error_message" : errorResponse,
            "source" : "REALTIME_SMS_PARSING_POSTPAID",
            "source_kafka_topic": `${source_kafka_topic}`,
            "sender_id" : _.get(record, 'senderId', null),
            "payload" :  record,
            "amount" : utility.getFilteredAmount(_.get(record, 'telecom_details.due_amount', _.get(record, 'amount', '0'))),
            "dataConsumed" : utility.getFilteredAmount(_.get(record, 'dataConsumed', null)),
            "ruOnboarded" : ruOnboarded,
            "due_date" : _.get(processedRecord, 'dueDate', null),
        }
        return new Promise((resolve)=>{
            self.bills.ingestRealtimePayloads(dbRecord,self.realtimePayloadIngestionTable,function(err,data){
                if(err){
                    self.L.error(`ingestCCPayloadInDB :: Error while inserting payload in table ${self.realtimePayloadIngestionTable} err:: ${err} for record ${JSON.stringify(dbRecord)}`);
                }else{
                    self.L.log(`ingestCCPayloadInDB :: Payload inserted successfully in table ${self.realtimePayloadIngestionTable} for record ${JSON.stringify(dbRecord)}`);
                }
                return resolve(null);
            })
        })
    }
    publishInBillFetchKafka(done,processedRecord){
        let self = this;
        let dbData = _.get(processedRecord, 'dbData', []);
        self.L.log(`10. publishInBillFetchKafka:: Record Category - ${processedRecord.service}`);

        // if(_.get(processedRecord, 'isRuSmsParsing', false)==false || _.get(processedRecord,'isDwhSmsParsing',false) == false){
        //     self.L.log("publishToBillFetchKafka:: ", 'Not publishing on REMINDER_BILL_FETCH_REALTIME kafka due to failing eligibility')
        //     return done(null);
        // }

        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;

            if(dataRow.notification_status == 0){
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();                         
            }

            if(dataRow.is_automatic != 0 && dataRow.is_automatic != 5 && dataRow.is_automatic != 8){
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for automatic status : ${dataRow.is_automatic} debugKey::`, dbDebugKey);
                return cb();                         
            }
            
            if(dataRow.status == _.get(self.config , ['COMMON','bills_status','DISABLED'],7) || dataRow.status == _.get(self.config , ['COMMON','bills_status','NOT_IN_USE'],13)){
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME as inactive record, status : ${dataRow.status} debugKey::`, dbDebugKey);
                return cb();
            }
            let { productId, circle } = fetchProductIdAndCircle(dataRow);

            let payload = {
                source: self.smsParsingBillsDwhRealtime == true ? "postpaidBillFetchDWHRealtime" : ((_.get(processedRecord,'isRuSmsParsing', false)==true) ? "postpaidBillFetchRealtime" : "postpaidBillFetchDWH"),
                notificationType: "BILLGEN",
                data: {
                    customerId: dataRow.customer_id,
                    rechargeNumber: dataRow.recharge_number,
                    productId: productId,
                    operator: processedRecord.operator,
                    amount: processedRecord.amount,
                    bill_fetch_date: MOMENT(),
                    paytype: "postpaid",
                    service: processedRecord.service,
                    circle: circle,
                    customerMobile:  dataRow.customer_mobile,
                    customerEmail: dataRow.customer_email,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    userData: dataRow.user_data,
                    billDate: processedRecord.billDate,
                    notification_status: dataRow.notification_status,
                    dueDate: processedRecord.dueDate,
                    customerOtherInfo: JSON.stringify(processedRecord),
                    planBucket: processedRecord.planBucket,
                    is_automatic: dataRow.is_automatic,
                    extra: _.get(processedRecord, ["billsData", "extra"], '{}'),
                    paymentDate : dataRow.paymentDate,
                    dwhKafkaPublishedTime: _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                }
            }

            if(processedRecord.status == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5) &&
            self.allowedServiceToSaveOldDueDate.indexOf(_.toLower(_.get(processedRecord, 'service', null))) > -1 && 
            !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(_.get(processedRecord, 'operator', null))) > -1) 
            && _.get(processedRecord, 'oldBillFetchDate', null) == MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss')){
                _.set(payload, "notificationType", "OLD_BILL_NOTIFICATION");
                _.set(payload, ["data", "status"], _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5));
            }
            
            let kafkaProducer;

            _.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());

            if(_.get(processedRecord,'isDwhSmsParsingRealtime', false)==true && (typeof self.parent.billFetchKafkaPublisher != "undefined") && self.parent.billFetchKafkaPublisher != null) {
                kafkaProducer = self.parent.billFetchRealTimeKafkaPublisher;
                self.L.log("publishInKafka :: isDwhSmsParsingRealtime is true. Publishing in reminder kafka");
            } else {
                self.L.log("publishInKafka :: isDwhSmsParsingRealtime is false. Publishing in recharges kafka");
                kafkaProducer = self.parent.billFetchKafkaPublisher;
            }
            if(self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord)) {
                payload.notificationType = "PREPAID_LOW_BALANCE";
            }

            self.L.log("publishInKafka :: Pushing data in topic : ",(_.get(processedRecord,'isRuSmsParsing', false)==true || _.get(processedRecord,'isDwhSmsParsingRealtime', false)==true)? _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', '') : _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''));
            utility.sendNotificationMetricsFromSource(payload)
            kafkaProducer.publishData([{
                topic: (_.get(processedRecord,'isRuSmsParsing', false)==true || _.get(processedRecord,'isDwhSmsParsingRealtime', false)==true)? _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', '') : _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:REMINDER_BILL_FETCH', 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:REMINDER_BILL_FETCH', 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: REMINDER_BILL_FETCH_REALTIME', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                }
                return cb(null);
            }, [200, 800]);
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });

        function fetchProductIdAndCircle(dataRow) {
            let productId, circle;
            if (dataRow.product_id && dataRow.circle && _.get(dataRow, "circle", "null").toLowerCase() != _.get(processedRecord, "circle", "null").toLowerCase()) {
                if(_.get(dataRow, "circle", "null").toLowerCase() != _.get(processedRecord, "circle", "null").toLowerCase()){
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PRODUCT_ID_UPDATE',
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                }
                self.L.info('Circle is updated from ', _.get(processedRecord, "circle"), ' to ', _.get(dataRow, "circle"), processedRecord.debugKey);
                productId = dataRow.product_id;
                circle = dataRow.circle;
            }
            else {
                productId = processedRecord.productId;
                circle = processedRecord.circle;
            }
            return { productId, circle };
        }
    }

    async processRecord(record, done) {
        let self = this;
        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('2. processRecord:: start processing current record', JSON.stringify(record));

        try{
            ASYNC.waterfall([
                (next)=>{
                    self.validateAndProcessRecord(async (errorResponse,processedRecord)=>{
                        let operator = processedRecord.operator || "NoOpertor";
                        if (errorResponse) {
                            self.L.error(`SMS_PARSING_POSTPAID :: VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${errorResponse}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                                `SERVICE:${category}`, 
                                'STATUS:ERROR', 
                                'TYPE:VALIDATION_FAILURE', 
                                'OPERATOR:' + operator, 
                                'REASON:' + errorResponse,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(errorResponse,processedRecord);
                        }else {
                            self.L.log(`SMS_PARSING_POSTPAID :: VALIDATION_SUCCESS`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                                `SERVICE:${category}`, 
                                'STATUS:SUCCESS',
                                'TYPE:VALIDATION_SUCCESS', 
                                'OPERATOR:' + operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(null,processedRecord);
                        }
                    }, record);
                },
                (processedRecord, next)=>{
                        self.getForwardActionFlow((err,action)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_POSTPAID :: getForwardActionFlow`, `invalid action found for: ${processedRecord.debugKey} with error ${err}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_POSTPAID', `SERVICE:${category}`, 'STATUS:ERROR','TYPE:NO_ACTION',`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                                next(err, action, processedRecord)
                            } else if(action == 'findAndUpdateToCassandra' || !processedRecord.recordFoundOfSameCustId){
                                let billsData = self.getBillsData(processedRecord);
                                    processedRecord.billsData = billsData;
                                self.L.log(`SMS_PARSING_POSTPAID :: getForwardActionFlow`, `action: ${action}`);
                                next(null ,action ,processedRecord);
                            } else {
                                self.L.log(`SMS_PARSING_POSTPAID :: getForwardActionFlow`, `action: ${action}`);
                                next(null ,action ,processedRecord);
                            }
                        },processedRecord)
                },
                (action, processedRecord, next)=>{
                    if(action === 'handlePrepaidRecord'){
                        let billsData = self.getBillsData(processedRecord);
                        processedRecord.billsData = billsData;
                        self.prepaidFlowManager.handlePrepaidRecord((err, processedRecord) => {
                            if (err) {
                                self.L.error(`SMS_PARSING_POSTPAID :: handlePrepaidRecord`, `failed with error ${err}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_POSTPAID', `SERVICE:${category}`, 'STATUS:ERROR','TYPE:HANDLE_PREPAID_RECORD',`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                                return next(err, processedRecord);
                            } else {
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_POSTPAID', `SERVICE:${category}`, 'STATUS:SUCCESS','TYPE:HANDLE_PREPAID_RECORD',`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                                const { publishInNonRu, publishInNotification } = self.getPublishFlags(processedRecord);
                                _.set(processedRecord, 'publishInNonRu', publishInNonRu);
                                _.set(processedRecord, 'publishInNotification', publishInNotification);
                                
                                self.L.log(`handlePrepaidRecord billGen :: callback :: Flags :: publishInNonRu: ${publishInNonRu}, publishInNotification: ${publishInNotification} for ${processedRecord.debugKey}`);

                                next(null, action, processedRecord);
                            }
                        }, processedRecord);
                    }else{
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next) => {
                    self.processRecodWithMulripleActions(next, action, processedRecord, category, record);
                }

            ], async function (error,processedRecord) {
                //     if(_.get(record, 'isRuSmsParsing', false)){
                //         await self.ingestIncomingPayloads(error, processedRecord,record)
                //     .catch((err)=>{
                //         if(err){
                //             self.L.error(`SMS_PARSING::ingestIncomingPayloads`, `couldn't save record with error ${err}`);
                //         }
                //     })
                // }
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                            `SERVICE:${category}`, 
                            'STATUS:PROCESS_RECORD_FAILURE', 
                            'SOURCE:POSTPAID_SMS', 
                            'TYPE:' + error,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        self.L.verbose(`SMS_PARSING_POSTPAID :: processRecords`, `Exception occured Error Msg:: ${error}`);
                    } else {
                        self.L.log(`SMS_PARSING_POSTPAID :: processRecords`, `Record processed `);
                    }
                return done();
            });
        }catch (err) {
            self.L.error('processRecord:: ', err);
            return done();
        }

    }

    processRecodWithMulripleActions(done, action, processedRecord, category, record) {
        let self = this,
            sqlRecord = _.cloneDeep(processedRecord),
            cassandraRecord = _.cloneDeep(processedRecord),
            promises = [];
        
        self.L.log(`processRecodWithMulripleActions :: action is ${action} and recordFoundOfSameCustId is ${cassandraRecord.recordFoundOfSameCustId}`);
        if(action == 'update') {
            if ((self.billPushServiceRegistration && self.billPushServiceRegistration.includes(_.get(processedRecord, 'service', null))) || (self.billPushOperatorRegistrationAllowed && self.billPushOperatorRegistrationAllowed.includes(_.get(processedRecord, 'operator', null)))) {
                self.L.log("Going to push data for billPush Registration as it is confirmed RU");
                self.BillPush.pushToRegistrationProcess(self.parent, processedRecord, processedRecord, 'smsParsed', 'ru');
            }
            let updateAction = self.processForwardAction(action, sqlRecord, "sql", category, record);
            promises.push(updateAction);
        }
        if(action == 'findAndUpdateToCassandra' || !cassandraRecord.recordFoundOfSameCustId || action == 'handlePrepaidRecord'){
            let nonPaytmRecord = self.processForwardAction(action, cassandraRecord, "cassandra", category, record);
            promises.push(nonPaytmRecord);
        }

        Promise.all(promises)
        .then(()=> {
            done();
        })
        .catch((e) => {
            self.L.error("processRecodWithMulripleActions :: error received :: ",e);
            done();
        })
    }

    processForwardAction(action, processedRecord, insertInDb, category, record) {
        let defer = Q.defer();
        let self = this;
        let recharge_number = processedRecord.rechargeNumber;

        try {
            ASYNC.waterfall([
                (next)=>{
                    self.L.log(`processForwardAction :: starting for action : ${action}, insertInDb: ${insertInDb} for recharge_number: ${processedRecord.rechargeNumber}`);

                    if(insertInDb == "cassandra" && (action != 'handlePrepaidRecord' || _.get(processedRecord, 'publishInNonRu', false))){
                        if(processedRecord.recordFoundOfSameCustId != undefined && !processedRecord.recordFoundOfSameCustId){
                            self.L.log(`SMS_PARSING_POSTPAID :: updateCassandra | Record found for same RN,but with new custId`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                                `SERVICE:${category}`, 
                                'STATUS:RECORD_NOT_FOUND_OF_SAME_CID',
                                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                                'TYPE:NON_PAYTM_EVENTS',
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                        }
                        self.updateCassandra((err)=>{
                             if(err){
                                self.L.error(`SMS_PARSING_POSTPAID :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                next(err,processedRecord);
                             }else{
                                self.L.log(`SMS_PARSING_POSTPAID ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                             }
                        }, processedRecord);
                    } else {
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next) => {
                    if(action == 'update' && insertInDb != "cassandra") {
                        self.validateUpdateForwardActionFlow(next, action, processedRecord);
                    } else {
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next)=>{
                    if(action == 'update' && insertInDb != "cassandra"){
                        self.updateDbRecord((err)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_POSTPAID :: updateDbRecord`, `error while updating for : ${processedRecord.debugKey}, error: ${err}`);
                                next(err,processedRecord);
                            }else{
                                _.set(processedRecord, 'ruOnboarded', true);
                                self.L.log(`SMS_PARSING_POSTPAID :: updateDbRecord`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, processedRecord, action);
                            }
                        },processedRecord);
                    }else {
                        self.L.log(`SMS_PARSING_POSTPAID :: nonPaytm user smsData || old SMS Data,  Record processed `);
                        next(null, processedRecord, action);
                    }
                },
                (processedRecord, action, next)=>{
                    if((action == 'update' && insertInDb != "cassandra") || _.get(processedRecord,'publishInNotification', false)){
                        self.publishInKafka((err)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_POSTPAID: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                                utility._sendMetricsToDD(1, [
                                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                                    `SERVICE:${category}`, 
                                    'SOURCE:PUBLISH_KAFKA',
                                    'STATUS:ERROR', 
                                    'TYPE:' + err]);
                                next(err,processedRecord);
                            }else next(null ,processedRecord);
                        },processedRecord,action);
                    }else next(null ,processedRecord);
                },
                (processedRecord, next)=>{
                    let service = _.get(processedRecord, 'service', null);
                    let source;
                    source = _.get(processedRecord, 'isRuSmsParsing', false)? `SMS_${service}_POSTPAID_REALTIME`:`SMS_${service}_POSTPAID`;
                    if(self.smsParsingBillsDwhRealtime) source = `SMS_${service}_POSTPAID_DWH_RT`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        next(null,processedRecord);
                    },source,self.timestamps, processedRecord.operator, processedRecord);
                }
            ], function(error, processedRecord) {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                        `SERVICE:${category}`, 
                        'STATUS:PROCESS_RECORD_FAILURE', 
                        'SOURCE:POSTPAID_SMS', 
                        'TYPE:' + error,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.error(`SMS_PARSING_POSTPAID :: processForwardAction`, `Exception occured Error Msg:: ${error}`);
                } else {
                    self.L.log(`SMS_PARSING_POSTPAID :: processForwardAction`, `Record processed `);
                }
                self.L.log(`processForwardAction :: process completed : ${action}, insertInDb: ${insertInDb} for recharge_number: ${recharge_number}`);
                defer.resolve();
            });
        } catch (e) {
            self.L.error('processForwardAction:: exception occured', e);
            defer.resolve();
        }
        return defer.promise;
    }


    validateAndProcessRecord(done,record) {
        let self = this;
        self.L.log('3. validateAndProcessRecord :: convert payload to record and validate');
        if (!record) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                'STATUS:ERROR', 
                'TYPE:RECORD_NULL',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            return done('invalid_record', record);
        }
        if(_.get(record, 'isRuSmsParsing', false)==false && !record.telecom_details && (!_.get(record,'isDwhSmsParsing',false) && !_.get(record,'isDwhSmsParsingRealtime',false))){
            if(self.saveForAnalyticsInCassandraAndKafka(record)) return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, null, null),"!record.telecom_details && (!_.get(record,'isDwhSmsParsing',false) && !_.get(record,'isDwhSmsParsingRealtime',false)",done, record);
            return done('invalid_record', record);
        }
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime(),
                    dwhKafkaPublishedTime = new Date(record.published_time).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
            _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
            _.set(self.timestamps, 'dwhKafkaPublishedTime', dwhKafkaPublishedTime);

        if(_.get(record,'isDwhSmsParsingRealtime',false) && (typeof _.get(record, 'smsDateTime', null) == "string") && _.get(record, 'smsDateTime', null) != null){
            _.set(record, 'smsDateTime', parseInt(_.get(record, 'smsDateTime', null)));
        }

        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000;
            }
        }else{
            _.set(record, 'smsDateTime', MOMENT().format('YYYY-MM-DD'));
        }

        let customerId =  (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
            amount = _.get(record, 'telecom_details.due_amount', _.get(record, 'amount', null)) ? utility.getFilteredAmount(_.get(record, 'telecom_details.due_amount', _.get(record, 'amount', null))) : null,
            dueDate = utility.getFilteredDate(_.get(record, 'telecom_details.due_date', _.get(record, 'dueDate', null))).value,
            billDate = utility.getFilteredDate(_.get(record, 'telecom_details.bill_date', _.get(record, 'billDate',null))).value || MOMENT(record.smsDateTime),
            operator = _.toLower(_.get(record, 'isRuSmsParsing', (_.get(record,'isDwhSmsParsing',false) || _.get(record,'isDwhSmsParsingRealtime',false))) ? _.get(record, 'operator', '') : _.get(record, 'telecom_details.operator', _.get(record, 'smsOperator', ''))),
            category = self.getServiceCategoryFromRecord(record);
        if(category == "MOBILE") operator = _.get(record, 'telecom_details.operator', _.get(record, 'smsOperator', ''));
        console.log("printing the operator before mapping :: ", operator);
        if (_.get(record, 'isPrepaid', "0") == "1" && _.get(record, 'amount', null) == 0) {
            amount = 0;
        }

        let rechargeNumber = _.get(record, 'rechargeNumber', null),
            isSmsReceiverPresent = false;
        if (!rechargeNumber && category == 'MOBILE') {
            rechargeNumber = _.get(record, 'telecom_details.mobile_number', null);
            if(!rechargeNumber || rechargeNumber == 'null') rechargeNumber = _.get(record, 'smsReceiver', null);
            isSmsReceiverPresent = true;
        }
        if((_.get(record,'isDwhSmsParsing',false) || (_.get(record,'isDwhSmsParsingRealtime',false) && category == "ELECTRICITY"))){
            operator = _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'DWH_OPERATOR_MAPPING', operator], _.get(record,'operator',null));
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:ELECTRICITY`, 
                'STATUS:TRAFFIC', 
                'TYPE:OPERATOR_AFTER_OPERATOR_MAPPING',
                'OPERATOR:' + operator,
                `ORIGIN:${_.get(record,'isDwhSmsParsingRealtime',false) == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
            ]);
        }

        operator = _.toLower(operator);
        
        let demergerOperatorsList = _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'DEMERGER_OPERATOR', operator], null);

        console.log("printing the operator after mapping :: ",operator);

        if (rechargeNumber && category=='MOBILE') {
            rechargeNumber = rechargeNumber.toString();
            if (rechargeNumber.length >= 10) {
                rechargeNumber = rechargeNumber.slice(-10);
            }else{
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:${category}`, 
                    'STATUS:ERROR', 
                    'TYPE:RN_LESS_THAN_10',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(record))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, null, null),"rechargeNumber Invalid : RN_LESS_THAN_10",done, record);
                return done('rechargeNumber Invalid', record);
            }

            // check recharge_number contains only digit
            if(!(/^\d+$/.test(rechargeNumber)))
            {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:${category}`, 
                    'STATUS:ERROR', 
                    'TYPE:RN_ALPHANUMERIC',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(record)) return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, null, null),"rechargeNumber Invalid : RN_ALPHANUMERIC",done, record);
                return done('rechargeNumber Invalid', record);
            }
        }   

        let processedRecord = {
            "operator": operator,
            "customerId": customerId,
            "rechargeNumber": rechargeNumber,
            "gateway": null,
            "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            "billDate": billDate ? billDate.endOf('day').format('YYYY-MM-DD') : null, 
            "dueDate": dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            "amount": amount,
            "status": _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            "paytype": 'postpaid',
            "cache": null,
            "service_id": _.get(self.recent_bills_operators, [operator, 'serviceId'], 0),
            "customerMobile": (category == 'MOBILE') ? rechargeNumber : null,
            "customerEmail": _.get(record, 'smsReceiverEmail', null),
            'paymentChannel': null,
            "retryCount": 0,
            "reason": null,
            "extra": null,
            "customer_type":null, 
            "paymentDate": null,
            "user_data":null,
            "msgId" : _.get(record, 'msg_id', ''),
            "rtspClassId" : _.get(record, 'rtspClassId', null),
            "dwh_classId" : _.get(record, 'level_2_category', null),
            "rtspClassName" : _.get(record, 'rtspClassName', null),
            "sender_id": _.get(record, 'smsSenderID', null),
            "sms_id" : _.get(record, 'msg_id', null),
            "sms_date_time" : _.get(record, 'smsDateTime', null),
            "category": category,
            "appVersion":  _.get(record, 'appVersion', null),
            "demergerOperatorsList":demergerOperatorsList,
            "smsDateTime": _.get(record, 'smsDateTime', null),
            "isPrepaid": _.get(record,'isPrepaid', "0"),
            "dwhKafkaPublishedTime": _.get(record, 'published_time', null),
            "isDuplicateCANumberOperator": _.get(record, 'isDuplicateCANumberOperator', false),
            "alternateRechargeNumber": _.get(record, 'alternateRechargeNumber', null),
            "alternateCAExistsInDB": _.get(record, 'alternateCAExistsInDB', false),
        };

        if(dueDate == null || amount == null){
            processedRecord.partialRecordFound = true;
        }

        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(processedRecord, 'isRuSmsParsing', true);
        }else if(_.get(record,'isDwhSmsParsing',false) == true){
            _.set(processedRecord, 'isDwhSmsParsing', true);
        } else if(_.get(record,'isDwhSmsParsingRealtime',false) == true){
            _.set(processedRecord, 'isDwhSmsParsingRealtime', true);
        }
        

        let minAmount= _.get(self.config, ['DYNAMIC_CONFIG', 'MOBILE_POSTPAID_SMS_PARSING', 'COMMON', 'MIN_AMOUNT'], 10);
        let maxAmount;
        
        if(_.get(record, 'isRuSmsParsing', false) == false && (_.get(record,'isDwhSmsParsing',false) == false) && (category == "MOBILE" && _.get(record,'isDwhSmsParsingRealtime',false) == true)){
            maxAmount= _.get(self.config, ['DYNAMIC_CONFIG', 'MOBILE_POSTPAID_SMS_PARSING', 'COMMON', 'MAX_AMOUNT'], 10000);
        }

        if(operator && ((_.get(record,'isDwhSmsParsing',false) == false ) || (category == "MOBILE" && _.get(record,'isDwhSmsParsingRealtime',false) == true))){
            _.set(processedRecord, 'circle' , 'all circles');
            _.set(processedRecord, 'productId' , _.get(self.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', operator, 'PRODUCT_ID'], null));   
        }

        self.L.log('3. validateAndProcessRecord :: payload after processing',processedRecord);

        let rechargeNumber2 = _.toLower(_.get(record, 'rechargeNumber2', null));
        let pidMapKey = (operator + (rechargeNumber2 != null && rechargeNumber2 != '' ? `_${rechargeNumber2}` : '')).replace(/ /g, '_'); 
        if (!_.get(processedRecord, 'productId', null)) {
            // Set productId in case of electricity category
            if((_.get(record,'isDwhSmsParsing',false) == true) || (_.get(record,'isDwhSmsParsingRealtime',false) == true && category == "ELECTRICITY")){
                self.L.log('3. validateAndProcessRecord :: pidMapKey',pidMapKey);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:ELECTRICITY`, 
                    'TYPE:PID_MAP_KEY',
                    'PRODUCT_ID:' + pidMapKey,
                    `ORIGIN:${_.get(record,'isDwhSmsParsingRealtime',false) == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
                ]);
                _.set(processedRecord, 'productId' , _.get(self.config,['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', pidMapKey], _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null)));
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:ELECTRICITY`, 
                    'TYPE:OPERATOR_AFTER_PRODUCT_ID',
                    `PRODUCT_ID: ${_.get(processedRecord,'productId','NO_PRODUCT_ID')}`,
                    `ORIGIN:${_.get(record,'isDwhSmsParsingRealtime',false) == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
                ]);
            }
            else{
                _.set(processedRecord, 'productId' , _.get(self.config,['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', pidMapKey], _.get(self.config, ['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null)));
            }
        }
        if(_.get(processedRecord, 'productId', null)){
            try {
                _.set(processedRecord, 'paytype', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'paytype'])),null),
                _.set(processedRecord, 'service', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'service'])), null);
            } catch(err) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                    `SERVICE:${category}`,
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:SETTING_PAYTYPE_SERVICE'
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                self.L.error("Couldn't set paytype and service from cvr for record ", JSON.stringify(processedRecord))
            }
        }

        let isValidRechargeNumber = this.regexExecutor.checkValidityOfRechargeNumberByRegex(processedRecord);
        if(!isValidRechargeNumber){
            self.publishFailedRecordInKafka(record, function(){});
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),"rechargeNumber Invalid : Invalid (Regex)",done, processedRecord);
            return done('rechargeNumber Invalid (Regex)', processedRecord);
        }

        let debugKey = `rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`;
            _.set(processedRecord, 'debugKey', debugKey);

        let mandatoryParams = ['customerId', 'rechargeNumber', 'operator', 'productId'];
        
        let fieldsNotPresent = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) fieldsNotPresent.push(key);
        });

            // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR', 
                'OPERATOR:' + processedRecord.operator,
                'TYPE:MANDATORY_PARAMS_NOT_PRESENT',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),`Mandatory Params ${fieldsNotPresent} is Missing / Invalid`,done, processedRecord);
            return done(`Mandatory Params ${fieldsNotPresent} is Missing / Invalid`, processedRecord);
        }

        if(self.checkIfOldSMSRejectionCase(processedRecord)){
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR', 
                'OPERATOR:' + processedRecord.operator,
                'TYPE:OLD_SMS_REJECTION',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),'Old SMS Date Found',done, processedRecord);
            return done('Old SMS Date Found', processedRecord);
        }
        if (processedRecord.amount != null && processedRecord.amount < minAmount && processedRecord.isPrepaid == "0") {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR', 
                'OPERATOR:' + processedRecord.operator,
                'TYPE:AMOUNT_LESS_THAN_MIN_AMOUNT',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),'Amount less than minAmount',done, processedRecord);
            return done('Amount less than minAmount', processedRecord);
        } else if (processedRecord.amount != null && processedRecord.amount > maxAmount) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR', 
                'OPERATOR:' + processedRecord.operator,
                'TYPE:AMOUNT_MORE_THAN_MAX_AMOUNT',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),'Amount greater than maxAmount',done, processedRecord);
            return done('Amount greater than maxAmount', processedRecord);
        } else if (processedRecord.dueDate && !MOMENT(processedRecord.dueDate).isValid()) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR',
                'OPERATOR:' + processedRecord.operator,
                'TYPE:DUE_DATE_INVALID',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),'DueDate is invalid',done, processedRecord);
            return done('DueDate is invalid', processedRecord);
        } else if (processedRecord.dueDate && MOMENT(processedRecord.dueDate).isValid() && MOMENT(processedRecord.dueDate).diff(MOMENT().endOf('day'), 'days') < 0) {
            if (self.allowedServiceToSaveOldDueDate.includes(_.toLower(category))) {
                _.set(processedRecord, 'status', _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5))
                self.L.log(`validateAndProcessRecord :: old bill received for custId: ${processedRecord.customerId}, recharge number: ${processedRecord.rechargeNumber}, service: ${category}, operator: ${processedRecord.operator}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:${category}`, 
                    'STATUS:TRAFFIC',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:OLD_BILL_FOUND',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:${category}`, 
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:DUE_DATE_IN_PAST',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),'DueDate is in past',done, processedRecord);
                return done('DueDate is in past', processedRecord);
            }
        }

        let activePid = self.activePidLib.getActivePID(processedRecord.productId);
            self.L.verbose('processRecord', `Found active Pid ${activePid} against PID ${processedRecord.productId}`);
            processedRecord.oldProductId = processedRecord.productId; // Keeping track of original PID
            processedRecord.productId = activePid;    // Replacing active PID

        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.operator], null);

        if (!tableName){
            self.L.error(`processRecord:: ${processedRecord.operator} not migrated`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                `SERVICE:${category}`, 
                "STATUS:ERROR",
                'TYPE:TABLE_NOT_FOUND', 
                'OPERATOR:' + processedRecord.operator,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),`Table not found for given operator`,done, processedRecord);
            return done(`Table not found for ${processedRecord.operator}`,processedRecord);
        }
        self.L.log(`processRecord:: table_name found for operator: ${processedRecord.operator}:${tableName}`);
        _.set(processedRecord, 'tableName', tableName);

        let nextBillFetchDate = self.getNextBillFetchDate(processedRecord);
            self.L.log(`SMS_PARSING_POSTPAID: getNextBillFetchDate for sms Data, NBFD: ${nextBillFetchDate}`);
            _.set(processedRecord, 'nextBillFetchDate', nextBillFetchDate);

        self.L.log('validateAndProcessRecord :: final payload returning',JSON.stringify(processedRecord));
        return done(null, processedRecord);

    }
    
    async getForwardActionFlow(done, processedRecord){
        let self = this;
        self.L.log('4. getForwardActionFlow:: starting getForwardActionFlow');
        self.getRecordsFromDb((err, recordsFound) =>{
            if(err){
                self.L.error(`SMS_PARSING_POSTPAID: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                    `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                    'STATUS:ERROR', 
                    'TYPE:ERROR_GETTING_RECORD_FROM_DB', 
                    `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                    'SOURCE:getForwardActionFlow',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),`ERROR in getRecordsFromDb with error: ${err}`,done);
                return done(err);
            }else{
                self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound :` , `for the processedRecord: ${processedRecord.debugKey}`);
                if(self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord)) {
                    self.L.log(`processRecord:: prepaid flow allowed for ${processedRecord.rechargeNumber}. Returning handlePrepaidRecord action`);
                    const now = MOMENT().startOf('day');
                    const nfbDays = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'NBFD_CYCLE_BILLGEN', processedRecord.operator], 15);
                    processedRecord.commonDueDate = now.format('YYYY-MM-DD HH:mm:ss');
                    processedRecord.dueDate = now.format('YYYY-MM-DD HH:mm:ss');
                    processedRecord.nextBillFetchDate = now.add(nfbDays, 'days').format('YYYY-MM-DD HH:mm:ss');
                    const amount = _.get(processedRecord, 'amount', null);
                    processedRecord.commonAmount = amount !== null ? Math.abs(amount) : null;
                    self.L.verbose(`getForwardActionFlow:: going to handlePrepaidRecord action for ${processedRecord.debugKey}, returning payload ${JSON.stringify(processedRecord)}`);
                    return done(null, 'handlePrepaidRecord', processedRecord);
                }

                if(recordsFound){
                    let dbRecord = _.get(processedRecord, 'dbData', {}),
                        dbDueDate = _.get(dbRecord, '[0].due_date', null);

                    let oldDueDate = dbDueDate ? MOMENT(dbDueDate).utc().startOf('day') : null;
                    self.L.log('postpaidSmsParsing::getForwardActionFlow', 'oldDueDate:', oldDueDate);
                    let newDueDate = processedRecord.dueDate ? MOMENT(processedRecord.dueDate).utc().startOf('day') : null;
                    self.L.log('postpaidSmsParsing::getForwardActionFlow', 'newDueDate:', newDueDate);
                    newDueDate = newDueDate ? newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG', 'RU', 'DEFAULT_DIFF_DAYS'], 5), 'days') : null;
                    if (oldDueDate && newDueDate && newDueDate.isAfter(oldDueDate, 'day')) {
                        self.L.log('postpaidSmsParsing::getForwardActionFlow', 'Due date difference is more than threshold, resetting remind later date');
                        _.set(processedRecord, 'resetRemindLaterDate', true);
                    } 
                    return done(null, 'update', processedRecord);   
                }else{
                    self.L.log(`processRecord:: No recordsFound in DB for the processedRecord: ${processedRecord.debugKey}`);
                    return done(null, 'findAndUpdateToCassandra' , processedRecord);
                }
            }
        }, processedRecord);

    }

    validateUpdateForwardActionFlow (done, action,processedRecord) {
        let self = this;
        self.L.log(`validateUpdateForwardActionFlow:: ${processedRecord.noOfFoundRecord} recordsFound :` , `for the processedRecord: ${processedRecord.debugKey}`);
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:SMS_PARSING_POSTPAID',
            `SERVICE:${_.get(processedRecord, 'category', null)}`, 
            'STATUS:SUCCESS', 
            `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
            'TYPE:RECORD_FOUND_IN_DB', 
            'SOURCE:POSTPAID_SMS',
            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
            `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
        ]);

        if (_.isNull(processedRecord.dueDate) || _.isNull(processedRecord.amount)) {
            self.L.log(`processRecord:: DueDate or Amount found as null, so skipping this record ${processedRecord.debugKey}`);    
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                'STATUS:ERROR', 
                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                'TYPE:DUE_DATE_AMOUNT_NULL', 
                'SOURCE:getForwardActionFlow',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),`DueDate or Amount found as null`,done);
            return done('DueDate or Amount found as null');
        }

        if(_.get(processedRecord,'recordFoundOfSameCustId',null) == false){
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                'STATUS:ERROR', 
                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                'TYPE:RECORD_FOUND_OF_DIFFERENT_CUSTOMER_ID', 
                'SOURCE:getForwardActionFlow',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
            ]);
        }

        let dbRecord = _.get(processedRecord, 'dbData', {}),
            dbDueDate = _.get(dbRecord, '[0].due_date', null),
            curDbDueDateDiff = MOMENT().startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day')),
            dueDateDiff =  MOMENT(processedRecord.dueDate).startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day'), 'day'),
            dbStatus = _.get(dbRecord, '[0].status', null),
            amount = _.get(processedRecord, 'amount', null),
            dbAmount = _.get(dbRecord, '[0].amount', null),
            maxPaymentDate = _.get(processedRecord, 'maxPaymentDate', null),
            maxBillDate = _.get(processedRecord, 'maxBillDate', null);
        
        let amountDiff = Math.abs(amount - dbAmount);
        if (MOMENT(processedRecord.dueDate) < MOMENT() && self.allowedServiceToSaveOldDueDate.includes(_.toLower(_.get(processedRecord, 'category', ''))) && dueDateDiff === 0 && maxPaymentDate && maxBillDate && MOMENT(maxPaymentDate) < MOMENT(maxBillDate)) {
            _.set(processedRecord.billsData, amountDiff?'updateForOldBillAmountDiff':'updateForOldBillAmountSame', true);
        }

        if (MOMENT(dbDueDate).isValid() && MOMENT(processedRecord.dueDate).isValid() && MOMENT(processedRecord.dueDate).isSameOrBefore(MOMENT(dbDueDate)) && maxPaymentDate && maxBillDate && MOMENT(maxPaymentDate) > MOMENT(maxBillDate)) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:UPMS_BILL_UPDATE",
                `SERVICE:${_.get(processedRecord, 'service', null)}`,
                "STATUS:ERROR",
                `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                'TYPE:PAYMENT_ALREADY_DONE',
                'SOURCE:getForwardActionFlow'
            ]);
            self.L.log('SMS_PARSING_MOBILE_POSTPAID:: Payment Event already processed for: ', processedRecord.debugKey);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),'payment already done',done);
            return done("payment already done");
        }

        if(MOMENT(dbDueDate).isValid() && (dueDateDiff < 0)){
                self.L.log(`SMS_PARSING_MOBILE_POSTPAID: getRecordsFromDb , smsDueDate:${MOMENT(processedRecord.dueDate).startOf('day').format('YYYY-MM-DD')} < dbDueDate:${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}`);
                processedRecord.dueDate = dbDueDate;
                utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_POSTPAID",
                `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                "STATUS:ERROR",
                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                'TYPE:DUE_DATE_IN_DB_GREATER', 
                'SOURCE:POSTPAID_SMS',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),'smsDueDate less than dbDueDate',done);
                return done('smsDueDate less than dbDueDate');
        }
        else if (MOMENT(dbDueDate).isValid() && (curDbDueDateDiff < 1)) {
            self.L.log(`lib:bills: getRecordsFromDb ,dbDueDate:${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:UPMS_BILL_UPDATE",
                `SERVICE:${_.get(processedRecord, 'service', null)}`,
                "STATUS:ERROR",
                `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                'TYPE:DB_DUE_DATE_IN_FUTURE',
                'SOURCE:getForwardActionFlow'
            ]);
            //if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), 'smsDueDate less than dbDueDate', done);
            return done('DbDueDate is present in future');
        }
        else if(dbStatus== _.get(self.config , ['COMMON','bills_status','DISABLED'],7) || dbStatus== _.get(self.config , ['COMMON','bills_status','NOT_IN_USE'],13)){
            self.L.log(`SMS_PARSING_MOBILE_POSTPAID: getRecordsFromDb , dbStatus: ${dbStatus}`);
            processedRecord.dueDate = dbDueDate;
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_POSTPAID",
                `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                "STATUS:ERROR",
                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                'TYPE:INACTIVE_RECORD', 
                'SOURCE:POSTPAID_SMS',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),'inactive record in db',done);
                return done('inactive record in db');
        }
        let billsData = self.getBillsData(processedRecord);
        processedRecord.billsData = billsData;
        done(null,action,processedRecord);
    }

    async updateBillsInRecent(done, record) {
        let self = this;
        self.L.log(`8. updateBillsInRecent`, `Going to update in recent for ${record.debugKey}`);
        let queryParam = {
            recharge_number: _.get(record, 'rechargeNumber', null),
            operator: _.get(record, 'operator', null),
            paytype: _.get(record, 'paytype', null),
            service: _.get(record, 'service', null)
            }, fieldValue = {
            due_date: record.dueDate,
            bill_date: record.billDate,
            amount: _.get(record, 'amount', null),
            original_due_amount: _.get(record, 'amount', null),
            label: _.get(record, 'amount', null) && _.get(record, 'dueDate', null) ? `Bill Payment of Rs${_.get(record, 'amount', null)} due on ${MOMENT(record.dueDate).format('DD MMM YYYY')}` : null
        };
        self.recentsLayerLib.update(function (error) {
            self.L.log('updateBillsInRecent::recentsLayerLib.update', `update recents request completed for ${record.debugKey},error if any is:${error}`);
            return done(error);
            }, queryParam, "bills", [fieldValue], "smsParsingPostpaid");                     
    } 

    async updateDbRecord(done,record) {
            let self = this;
            let billsData = record.billsData;
            _.set(billsData, 'isDemergerCase', _.get(record, 'isDemergerCase', false));
            self.L.log('6. updateDbRecord:: starting updateDbRecord');
            if(record.activeRecordsInDB == 0){
                self.L.log('updateRecord', `No actve records in DB, so skipping update for ${record.debugKey}`);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                    `SERVICE:${_.get(record, 'category', null)}`, 
                    'STATUS:ERROR',
                    'TYPE:NO_ACTIVE_RECORDS', 
                    "OPERATOR:" + record.operator,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(record))return self.saveAndPublishBillFetchAnalyticsDataWithoutError(self.createRecordForAnalytics1(record, (record.dueDate ==null || record.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),'No actve records in DB, so skipping update',done);
                return done(null);
            } else if (_.get(record, 'is_automatic', 0) != 0) {
                self.L.log('updateDbRecord::', `automatic record, updating NBFD for ${record.debugKey}`);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                    `SERVICE:${_.get(record, 'category', null)}`, 
                    'STATUS:TRAFFIC', 
                    'TYPE:UPDATE_BILLS_NBFD', 
                    "OPERATOR:" + record.operator,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                ]);
                self.bills.updateBillsNBFD(done, record.tableName, record);
            } else {
                _.set(billsData, 'is_automatic', null);
                self.L.log('updateRecord', `Updating records in sql DB for ${record.debugKey}`);
                self.bills.updateBillForSameRechargeNumPostpaid((err) => {
                    _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
                    if (err) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                            `SERVICE:${_.get(record, 'category', null)}`, 
                            'STATUS:ERROR', 
                            "TYPE:UPDATE_SQL",
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        if(self.saveForAnalyticsInCassandraAndKafka(record))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(record, (record.dueDate ==null || record.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),err,done);
                        return done(err);
                    } else { 
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                            `SERVICE:${_.get(record, 'category', null)}`, 
                            'STATUS:SUCCESS', 
                            'TYPE:UPDATE_SQL', 
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        self.bills.updateBillSource(function () {
                            // self.updateBillsInRecent((err)=>{
                                _.set(self.timestamps,'RUupdateRecentTime',new Date().getTime());
                                // if(err){
                                //     utility._sendMetricsToDD(1, [
                                //         "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                //         `SERVICE:${_.get(record, 'category', null)}`, 
                                //         'STATUS:ERROR', 
                                //         'TYPE:UPDATE_RECENT', 
                                //         "OPERATOR:" + record.operator,
                                //         `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                //         `APP_VERSION:${_.get(record,'appVersion', null)}`
                                //     ]);
                                //     return done(null)
                                // }else{
                                    // utility._sendMetricsToDD(1, [
                                    //     "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                    //     `SERVICE:${_.get(record, 'category', null)}`, 
                                    //     'STATUS:SUCCESS', 
                                    //     'TYPE:UPDATE_RECENT', 
                                    //     "OPERATOR:" + record.operator,
                                    //     `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                    //     `APP_VERSION:${_.get(record,'appVersion', null)}`
                                    // ]);
                                    if(record.is_automatic == 3 || record.is_automatic == 4) {          //automatic failed case
                                        self.L.log('updateRecord::', `resetIsAutomatic with tableName: `+record.tableName+ `, rechargeNumber: `+record.rechargeNumber);
                                        self.bills.resetIsAutomatic((err)=>{
                                            _.set(self.timestamps,'RUupdateRecentTime',new Date().getTime());
                                            if(err){
                                                utility._sendMetricsToDD(1, [
                                                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                                    `SERVICE:${_.get(record, 'category', null)}`, 
                                                    'STATUS:ERROR', 
                                                    'TYPE:UPDATE_SQL',
                                                    "OPERATOR:" + record.operator,
                                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                                                ]);
                                                return done(err);
                                            }else{
                                                utility._sendMetricsToDD(1, [
                                                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                                    `SERVICE:${_.get(record, 'category', null)}`, 
                                                    'STATUS:SUCCESS', 
                                                    'TYPE:UPDATE_SQL', 
                                                    "OPERATOR:" + record.operator,
                                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                                                ]);
                                                return done(null);
                                             }
                                        }, record.tableName, record.rechargeNumber, [3,4], {isDemergerCase: record.isDemergerCase, customerId: record.customerId});
                                        self.L.log('resetIsAutomatic::', "reseting is_autoamtic complete in smsParsing");
                                    } else {
                                        self.L.log('updateRecord::', `resetIsAutomatic is not required`);
                                        return done(null);
                                    } 
                                //  }
                            // },record);
                        }, record.tableName, 'sms', self.getOriginOfPayloadCurrentlyBeingProcessed(record), record.customerId, record.rechargeNumber);
                    }
                }, record.tableName, billsData);
            }
    }

    async updateCassandra(done, processedRecord) {
        let self = this;
        self.L.log('5. updateCassandra:: starting updateCassandra');
        try {
            let extra = {};
            extra.eventState = "bill_gen";
            extra.billSource = "sms_parsed";
            extra.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);
            extra.created_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);
            extra.demergerOperatorsList = _.get(processedRecord, 'demergerOperatorsList', null);

            if(_.get(processedRecord,'isRuSmsParsing', false)){
                extra.isRuSmsParsing = true;
            }
            if(_.get(processedRecord,'isDwhSmsParsing',false)){
                extra.isDwhSmsParsing = true;
            } else if(_.get(processedRecord,'isDwhSmsParsingRealtime',false)) {
                extra.isDwhSmsParsingRealtime = true;
            }

            if(self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord)){
                extra.isPrepaid = "1";
                if (_.get(processedRecord, 'partialRecordFound', false)) {
                    _.set(extra, 'partialBillState', "LOW_BALANCE_PARTIAL_RECORD");
                }
            }

            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId,
                rechargeNumber: processedRecord.rechargeNumber,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                amount: processedRecord.amount,
                dueDate : MOMENT(processedRecord.dueDate).isValid() ? MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss') : null,
                billDate :  MOMENT().format('YYYY-MM-DD HH:mm:ss'),   
                billFetchDate : MOMENT(processedRecord.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
                nextBillFetchDate: MOMENT(processedRecord.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss'),
                paytype: processedRecord.paytype,
                service: processedRecord.service,
                circle: processedRecord.circle,
                categoryId: _.get(self.config, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
                customer_mobile:  null,
                customer_email: null,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),  
                customerOtherInfo: _.get(processedRecord,['billsData','customerOtherInfo'],'{}'),   
                extra : JSON.stringify(extra),                        
                dbEvent: "upsert",
                dwhClassId: _.get(processedRecord, 'dwhClassId', null),
                rtspClassId: _.get(processedRecord, 'rtspClassId', null),
                source: self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord),
                demergerOperatorsList: _.get(processedRecord, 'demergerOperatorsList', null),
                dwhKafkaPublishedTime : _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                nonpaytm_onBoardTime : new Date().getTime(),
                isDuplicateCANumberOperator: _.get(processedRecord, 'isDuplicateCANumberOperator', false),
                alternateRechargeNumber: _.get(processedRecord, 'alternateRechargeNumber', null),
                alternateCAExistsInDB: _.get(processedRecord, 'alternateCAExistsInDB', false),
            }
            
            if(_.get(processedRecord, 'resetRemindLaterDate', false)){
                _.set(dataToBeInsertedInDB, 'remind_later_date', null);
            }

            if(processedRecord.partialRecordFound){
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:${processedRecord.service}`, 
                    "STATUS:SUCCESS",
                    'TYPE:PARTIAL_RECORD', 
                    'OPERATOR:' + processedRecord.operator,
                    `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                ]);
                _.set(dataToBeInsertedInDB, 'partialSmsFound',true);
            }

            if(_.get(processedRecord,'service',null)=="mobile"){
                _.set(dataToBeInsertedInDB, 'toBeNotified', true);
            }
            //publish in billfetch
            let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);

            self.parent.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('SMS_PARSING_POSTPAID :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"),'Error while publishing message in Kafka',done);
                    return done('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('SMS_PARSING_POSTPAID :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', nonRuDataToPublish);
                    return done(null);
                }
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"),`updateCassandra error: ${error}`,done);
            done(error);
        }
    }

    async getRecordsFromDb(done,record){
        let self = this;
        if (_.get(record, 'demergerOperatorsList', null) != null && _.isArray(record.demergerOperatorsList)) {
            let oldOperator = record.operator;
            _.set(record, 'isDemergerCase', true);
            self.iterateOnDbs(record, (err, foundInDb) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_ITERATE_DB",
                        'STATUS:ERROR',
                        "OPERATOR:" + record.operator
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log('getRecordsFromDb :: not found in all DBs');
                    return done(err, false);
                } else if (foundInDb == 0) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_ITERATE_DB",
                        'STATUS:FAILURE',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_NOT_FOUND"
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log('getRecordsFromDb :: not found in all DBs');
                    return done(null, false);
                } else if (foundInDb == 1) {
                    self.L.log('getRecordsFromDb :: found in one of DBs');
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_ITERATE_DB",
                        'STATUS:SUCCESS',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_FOUND"
                    ]);
                    return done(null, true);
                } else {

                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_ITERATE_DB",
                        'STATUS:FAILURE',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_FOUND_IN_MULTIPLE_DB",
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log(`getRecordsFromDb :: found in multiple DBs::${foundInDb}`);
                    return done("found in multiple DBs");
                }
            });
        } else {
                self.L.log('getRecordsFromDb :: not array operator');
                self.bills.getBillsOfSameRech((err, data) => {
                    if (err) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_POSTPAID_GETRECORDS", 
                            'STATUS:ERROR', 
                            "OPERATOR:" + record.operator
                        ]);
                        return done(err, false);
                    }
                    if (!data || !_.isArray(data) || data.length < 1) return done(null, false);

                    let maxPaymentDate = data.reduce((prev, current) => {
                        if (!current.payment_date || !MOMENT(current.payment_date).isValid()) {
                            return prev;
                        }
                        if (!prev.payment_date || !MOMENT(prev.payment_date).isValid()) {
                            return current;
                        }
                        return MOMENT(prev.payment_date).isSameOrAfter(MOMENT(current.payment_date)) ? prev : current;
                    }, {});

                    let maxBillDate = data.reduce((prev, current) => {
                        if (!current.bill_date || !MOMENT(current.bill_date).isValid()) {
                            return prev;
                        }
                        if (!prev.bill_date || !MOMENT(prev.bill_date).isValid()) {
                            return current;
                        }

                        return MOMENT(prev.bill_date).isSameOrAfter(MOMENT(current.bill_date)) ? prev : current;
                    }, {});
                     
                    let recordOfSameCustId = self.checkIfRecordOfSameCustId(data, record);

                    _.set(record, 'maxBillDate', _.get(maxBillDate, 'bill_date', null));
                    _.set(record, 'maxPaymentDate', _.get(maxPaymentDate, 'payment_date', null));
                    _.set(record, 'noOfFoundRecord', data.length);
                    _.set(record, 'is_automatic', data[0].is_automatic);
                    _.set(record, 'dbData', self.getSortedDbData(data));
                    _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
                    _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
                    _.set(record, 'isRecordExist', true);

                    return done(null, true);
                }, record.tableName, record);
            }
    }

    iterateOnDbs(record, cb) {
        let self = this;
        let recordFoundInDb = 0;
        ASYNC.eachSeries(record.demergerOperatorsList, (subOperator, next) => {
                // in case table config is disabled, set main operator as tablename
            let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', subOperator], null);
            let clonedRecord = _.cloneDeep(record);
            _.set(clonedRecord, 'operator', subOperator);
            if (tableName != null) {
                try {
                    self.bills.getBillsOfSameRech((err, data) => {
                        if (err) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_POSTPAID_GETRECORDS",
                                'STATUS:ERROR',
                                "OPERATOR:" + record.operator,
                                "REASON:QUERY_ERROR_FROM_ITERATION_ON_TABLE",
                                "TABLE_NAME:" + tableName
                            ]);
                            return next(err);
                        }
                        else {
                            if (!data || !Array.isArray(data) || data.length < 1) {
                                self.L.error('iterateOnDbs :: not found in one DB named', tableName);
                                return next();
                            } else {
                                recordFoundInDb += 1;
                                self.L.log('iterateOnDbs :: found in one DB named {}', tableName);

                                let maxPaymentDate = data.reduce((prev, current) => {
                                    if (!current.payment_date || !MOMENT(current.payment_date).isValid()) {
                                        return prev;
                                    }
                                    if (!prev.payment_date || !MOMENT(prev.payment_date).isValid()) {
                                        return current;
                                    }
                                    return MOMENT(prev.payment_date).isSameOrAfter(MOMENT(current.payment_date)) ? prev : current;
                                }, {});

                                let maxBillDate = data.reduce((prev, current) => {
                                    if (!current.bill_date || !MOMENT(current.bill_date).isValid()) {
                                        return prev;
                                    }
                                    if (!prev.bill_date || !MOMENT(prev.bill_date).isValid()) {
                                        return current;
                                    }
                                    return MOMENT(prev.bill_date).isSameOrAfter(MOMENT(current.bill_date)) ? prev : current;
                                }, {});
 

                                let recordOfSameCustId = self.checkIfRecordOfSameCustId(data, record);
                                _.set(record, 'noOfFoundRecord', data.length);
                                _.set(record, 'is_automatic', data[0].is_automatic);
                                _.set(record, 'dbData', self.getSortedDbData(data));
                                _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
                                _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
                                _.set(record, 'operator', subOperator);
                                _.set(record, 'tableName', tableName);
                                _.set(record, 'isRecordExist', true);
                                _.set(record, 'maxBillDate', _.get(maxBillDate, 'bill_date', null));
                                _.set(record, 'maxPaymentDate', _.get(maxPaymentDate, 'payment_date', null));
                                return next();
                            }

                        }
                    }, tableName, clonedRecord);
                    } catch (error) {
                        // Handle errors
                    return next();
                    }
            } else {
                    self.L.error(`iterateOnDbs:: ${record.operator} not migrated`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                        `SERVICE: ${record.category}`,
                        "STATUS:ERROR",
                        'TYPE:TABLE_NOT_FOUND',
                        'OPERATOR:' + record.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION: ${_.get(record, 'appVersion', null)}`
                    ]);
                return next();
                }
        }, function (err) {
            if (err) {
                self.L.error('iterateOnDbs:: Error in getting data from db error', err);
                return cb(err);
            } else {
                self.L.log('iterateOnDbs:: recordFoundInDb::', recordFoundInDb);
                return cb(null, recordFoundInDb);
            }
        });
    }

    checkIfRecordOfSameCustId(data, record) {
        if(record.isDuplicateCANumberOperator && record.alternateRechargeNumber){
            let alternateCAExistsInDB = data.filter((dataValue) => dataValue.customer_id == record.customerId && dataValue.recharge_number == record.alternateRechargeNumber).length > 0 ? true : false;
            _.set(record, 'alternateCAExistsInDB', alternateCAExistsInDB);
            data = data.filter((dataValue) => dataValue.recharge_number == record.rechargeNumber)
        }
        return data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;
    }

    getBillsData(record) { 
        let self = this;
        let dbRecord,
            dbExtra = {},
            dbCustomerOtherInfo = {},
            oldBillFetchDate = null;
        if(record.isRecordExist){
            try{
                dbRecord = _.get(record, 'dbData[0]', {});
                dbExtra = JSON.parse(_.get(dbRecord, 'extra', {}));
                if(!dbExtra){
                    dbExtra = {};
                }
                dbCustomerOtherInfo = JSON.parse(_.get(dbRecord , 'customerOtherInfo', {}));
                if(!dbCustomerOtherInfo){
                    dbCustomerOtherInfo = {};
                } 
            }catch(err){
                self.L.error("getBillsData", "Error in JSON parsing" + err);
            }
        }
        let custInfoValues = dbCustomerOtherInfo;
        let extraDetails = dbExtra;
        extraDetails.billSource = 'sms_parsed';
        extraDetails.updated_source = 'sms';
        extraDetails.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(record);
        if (_.get(record, 'status', null) == _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) {
            if (_.get(dbRecord, 'old_bill_fetch_date', null) != null) {
                oldBillFetchDate = MOMENT(_.get(dbRecord, 'old_bill_fetch_date', null)).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
            else {
                oldBillFetchDate = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
        }
        else {
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(record.smsTimeStamp).isValid() ? MOMENT(record.smsTimeStamp).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null);
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        }
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        extraDetails.created_source = self.getOriginOfPayloadCurrentlyBeingProcessed(record);
        let recon_id = utility.generateReconID(_.get(record , 'rechargeNumber',_.get(record,'recharge_number','')), _.toLower(record.service) == "financial services" ? _.get(record, 'bankName', null) : _.get(record, 'operator', null) ,_.get(record, 'amount', null) ,  _.get(record, 'dueDate', null) , _.get(record, 'billDate', null));
        extraDetails.recon_id = recon_id;
        extraDetails.user_type = "RU";

        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(extraDetails,'isRuSmsParsing', true);
        }
        if(_.get(record,'isDwhSmsParsing',false) == true){
            _.set(extraDetails,'isDwhSmsParsing',true)
        } else if(_.get(record,'isDwhSmsParsingRealtime',false) == true) {
            _.set(extraDetails,'isDwhSmsParsingRealtime',true)
        }
        if(_.get(record, 'partialRecordFound', false)){
            _.set(extraDetails, 'source_subtype_2', 'PARTIAL_BILL');
        }else{
            _.set(extraDetails, 'source_subtype_2', 'FULL_BILL');
        }
        if(_.get(extraDetails, 'errorCounters', null)){
            extraDetails.errorCounters = {};
        }
        if(self.prepaidFlowManager.isPrepaidFlowAllowed(record)){
            _.set(extraDetails, 'isPrepaid', "1");
            if (_.get(record, 'partialRecordFound', false)) {
                _.set(extraDetails, 'partialBillState', "LOW_BALANCE_PARTIAL_RECORD");
            } else {
                //Full bill
                if (_.get(extraDetails, 'partialBillState') === "LOW_BALANCE_PARTIAL_RECORD") {
                    delete extraDetails.partialBillState;
                }
            }
        }
        delete extraDetails.upmsRegistrationNumber;
        delete extraDetails.upmsBillPaymentToken;
        custInfoValues.msgId = _.get(record,'msgId','');
        custInfoValues.sms_id = _.get(record,'sms_id','');
        custInfoValues.sms_date_time = _.get(record,'sms_date_time','');
        custInfoValues.sender_id = _.get(record,'sender_id','');
        custInfoValues.dwh_classId = _.get(record,'dwh_classId',null);
        let billsData = {
            user_data: record.user_data,
            nextBillFetchDate: record.nextBillFetchDate, 
            billFetchDate: extraDetails.billFetchDate,
            commonAmount: record.amount,
            commonDueDate: record.dueDate,
            rechargeNumber: record.rechargeNumber,
            billDate: record.billDate,
            productId: record.productId,
            commonStatus: record.status,
            customerId: record.customerId,
            customerMobile: _.get(record, 'customerMobile', null),
            operator: record.operator,
            circle: record.circle,
            service: record.service,
            gateway: record.gateway,
            retryCount: 0,
            reason: null,
            paytype: _.get(record, 'paytype', null),
            customerEmail: _.get(record, 'customerEmail', null),
            service_id: _.get(record, 'service_id', 0),
            is_automatic: _.get(record, 'is_automatic', 0),
            msgId : _.get(record, 'msgId', ''),
            resetRemindLaterDate: _.get(record, 'resetRemindLaterDate', false),
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            oldBillFetchDate: oldBillFetchDate
        };

        if(self.prepaidFlowManager.isPrepaidFlowAllowed(record)) {
            const now = MOMENT().startOf('day');
            const nfbDays = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'NBFD_CYCLE_BILLGEN', record.operator], 15);
            billsData.commonDueDate = now.format('YYYY-MM-DD HH:mm:ss');
            billsData.nextBillFetchDate = now.add(nfbDays, 'days').format('YYYY-MM-DD HH:mm:ss');
            const amount = _.get(record, 'amount', null);
            billsData.commonAmount = amount;//!== null ? Math.abs(amount) : null;
            billsData.isPrepaid = "1";
        }

        try{
            let highestPriorityAmongestRows = self.billsLib.getHighestPriorityAmongestRows(_.get(record,'dbData',[]));
            let highestPublishedDateAmongestRows = self.billsLib.getHighestPublishedDateAmongestRows(_.get(record,'dbData',[]));
            billsData = self.billsLib.updateRecordWithOffsetNbfd(billsData, highestPriorityAmongestRows, highestPublishedDateAmongestRows);
        }catch(e){
            self.L.error("getBillsData", "Error in updating record with offset nbfd" + e);
        }

        return billsData;
    }

    /**
     * Returns active users for which smsparsing data will be updated
     * @param {*} dbRecords 
     */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }

     /**
     * sorting DB data based on due date in descending order
     * [Use case: If some record have status=13 or 7 then code gets wrong information that we do not have latest billing cycle]
     * @param {*} data 
     */
      getSortedDbData(dbRecords) {

        dbRecords.sort(function (record1, record2) {
            let isValidR1date = MOMENT(record1.due_date).isValid(false);
            let isValidR2date = MOMENT(record2.due_date).isValid(false);

            if (isValidR1date && isValidR2date) {
                let daysDiff = MOMENT(record1.due_date).diff(record2.due_date, 'days');
                if (daysDiff > 0) return -1;
                else return 1;
            } else if (isValidR1date) {
                return -1;
            } else if (isValidR2date) {
                return 1;
            } else {
                return 0;
            }
        });

        return dbRecords;
    }

    getNextBillFetchDate(record) {
        let self = this;
        let billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        let dateToBeUsed = billDateBasedGateways.indexOf(record.operator) > -1 && record.billDate ? record.billDate : record.dueDate;

        if (billDateBasedGateways.indexOf(record.operator) > -1 && !record.billDate) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                `SERVICE:${_.get(record, 'category', null)}`, 
                'STATUS:BILLDATE_ABSENT_BILLDATEBASEDGATEWAY', 
                "OPERATOR:" + record.operator
            ]);
        }

        let nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(record.operator) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(record.operator))), 'months') : MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(record.operator), 'days');
        if (nextBillFetchDate < MOMENT()) {
            self.L.error(`getNextBillFetchDate:: Change NBFD, currently set in past debugKey: ${record.debugKey} NBFD: ${nextBillFetchDate}`);
            nextBillFetchDate = MOMENT().add(1, 'days');
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                `SERVICE:${_.get(record, 'category', null)}`, 
                'STATUS:NBFD_SETTING_IN_PAST', 
                "OPERATOR:" + record.operator
            ]);
        }
        return nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');
    }

    getServiceCategoryFromRecord(record) {
        let key = _.get(record, 'category', null) ? _.get(record, 'category', null).toLowerCase() : null,
            serviceCategory = null;
        
        switch (key) {
            case 'mobile prepaid':
                serviceCategory = 'MOBILE';
                break;
            case 'electricity':
                serviceCategory = 'ELECTRICITY';
                break;
            default:
                // Set default in case of when category won't be coming in DWH flow
                serviceCategory = 'MOBILE'
                break;
        }
        return serviceCategory;
    }

    /**
     * In case we want to publish the current record too, we create a record in following format
     * @param {record} record 
     * @returns 
     */
     getNewRecordForPublishing(record){
        let newRecord = {
            customerId: record.customerId,
            rechargeNumber: record.rechargeNumber,
            productId: record.productId,
            operator: record.operator,
            amount: record.amount,
            billDate: record.billDate,
            dueDate: record.dueDate,
            billFetchDate: record.billFetchDate,
            nextBillFetchDate: record.nextBillFetchDate,
            gateway: record.gateway,
            paytype: record.paytype,
            service: record.service,
            circle: record.circle,
            customerMobile: record.customerMobile,
            customerEmail: record.customerEmail,
            paymentChannel: record.paymentChannel,
            retryCount: record.retryCount,
            status: record.status,
            reason: record.reason,
            extra: record.extra,
            published_date: record.published_date,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt,
            userData: record.user_data,
            notification_status: record.notification_status,
            paymentDate: record.paymentDate,
            service_id: record.service_id,
            customerOtherInfo: record.customerOtherInfo,
            is_automatic: record.is_automatic,
            billGen: record.billGen,
            source: record.source,
            machineId: record.machineId
        };
        return newRecord
    }


     /**
     * @param {*} record 
     */

    publishToAutomaticSync(done,record){
        let self=this,
        dbData = _.get(record, 'dbData', []);
        self.L.log('9. publishToAutomaticSync:: starting publishToAutomaticSync');

            if (!record.service || !record.operator || !record.rechargeNumber || !record.productId) {
                self.L.critical('publishInKafka :: invalid inputs ', record.service, record.operator, record.rechargeNumber, record.productId);
                return done('invalid inputs');
            }

            let publishedInAutomaticSync=false;
            ASYNC.eachLimit(dbData, 1, (dataRow, cb) => {
                let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
                if (dataRow.status == 13 || dataRow.status == 7) {
                    self.L.log('publishInKafka', `Skipping pulish to : AUTOMATIC_SYNC_TOPIC for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice/old record`);
                    return cb();
                }

                let billsData = record.billsData;
                
                dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
                if(record.is_automatic && record.is_automatic !== 0){
                    dataRow.is_automatic = record.is_automatic;
                }
                dataRow.bill_date = record.billDate ? MOMENT(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
                dataRow.amount = record.amount;
                dataRow.bill_fetch_date = _.get(billsData, 'billFetchDate', null);
                dataRow.next_bill_fetch_date = _.get(billsData, 'nextBillFetchDate', null);
                dataRow.status = _.get(billsData, 'commonStatus', 0);
                dataRow.extra = _.get(billsData, 'extra', null);
                dataRow.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);

                let row = self.commonLib.mapBillsTableColumns(dataRow);
                row.billGen = true;
                row.source = "postpaidBillFetchRealtime";
                row.machineId = OS.hostname(); 
                if (_.get(row, 'is_automatic', 0) !== 0 && _.get(row, 'is_automatic', 0) !== 5 && _.get(row, 'is_automatic', 0) !== 8 && publishedInAutomaticSync == false) {
                    publishedInAutomaticSync=true;
                    self.parent.kafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                        messages: JSON.stringify(row),
                        key: _.get(record, 'rechargeNumber','')
                    }], function (error) {
                        if (error) {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE: SMS_PARSING_POSTPAID", 
                                        `SERVICE:${_.get(record, 'category', null)}`, 
                                        'STATUS:ERROR',
                                        'TYPE:KAFKA_PUBLISH',
                                        'TOPIC:AUTOMATIC_SYNC', 
                                        "OPERATOR:" + record.operator,
                                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                                    ]);
                            self.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(row), error);
                        } else {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                        `SERVICE:${_.get(record, 'category', null)}`, 
                                        'STATUS:PUBLISHED',
                                        'TYPE:KAFKA_PUBLISH',
                                        'TOPIC:AUTOMATIC_SYNC', 
                                        "OPERATOR:" + record.operator,
                                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                                    ]);
                            self.L.log('Message published successfully in Kafka', ' on topic AUTOMATIC_SYNC', JSON.stringify(row));
                        }
                        return cb();
                    }, [200, 800]);
                } 
                else {
                    self.L.log('Skipping publish in Kafka', ' on topic AUTOMATIC_SYNC', 'Due to is_automatic = 0');
                    return cb();
                }
            }, (error, res) => {
                if (error) {
                    self.L.error("postpaidSmsParsing :: publishKafka ", "Error occured", error);
                }
                return done(error);
            });
    }

    async publishInKafka(done, record, action) {
        let self = this;
        ASYNC.parallel([
            function (cb) {
                self.publishToAutomaticSync(function(err){
                    cb(err);
                },record)
            },
            function (cb) {
                self.publishInBillFetchKafka(function(err){
                    cb(err)
                },record)
            },
            function (cb) {
                self.publishCtEvents(function(err){
                    cb(err)
                },record)
            },
        ], function(error) {
            if (error) {
                self.L.error('Error occurred during parallel tasks:', error);
            }
            done(error);
        });
    }

    /**
     * @param {*} record 
     */

    async publishCtEvents(done,record) {
        let self = this;
        self.L.log(`11. publishCtEvents:: Record Category - ${record.category}`);
        const productId = _.get(record, 'productId', 0);
        let dbData = _.get(record, 'dbData', []);
        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen');

            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if ((dataRow.status == 13 || dataRow.status == 7)) {
                self.L.log('publishInKafka', `Skipping pulish CT for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice record`);
                return cb();
            }

            if(self.commonLib.isCTEventBlocked(eventName)){
                self.L.info(`Blocking CT event ${eventName}`)
                return cb()
            }

            let billsData = record.billsData;
            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
        
            if(dataRow.notification_status == 0){
                self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();                         
            }
            
            if(dataRow.notification_status == null)
                dataRow.notification_status = 1
            dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
            dataRow.rtspClassId = _.get(record, 'rtspClassId', null);
            dataRow.dwhClassId = _.get(record, 'dwhClassId', null);

            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {                    
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.parent.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                `SERVICE:${_.get(record, 'category', null)}`, 
                                'STATUS:ERROR', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            self.L.critical('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                `SERVICE:${_.get(record, 'category', null)}`, 
                                'STATUS:PUBLISHED', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`,
                                `EVENT_NAME:${eventName}`
                            ]);
                            self.L.log('postpaidSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.log('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return cb(err)
            })
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });
    }

    publishFailedRecordInKafka(record, cb){
        let self = this;
        if(self.parent.failedSMSParsingPublisher){
            self.parent.failedSMSParsingPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.FAILED_SMS_PARSING_PUBLISHER.TOPIC', ''),
                messages: JSON.stringify({"data": [record], "kafka_topic": ["FAILED_SMS_PARSING_REGEX_BACKUP"]})
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:FAILED_SMS_PARSING_REGEX_BACKUP", 
                        "OPERATOR:" + _.get(record, 'operator', null),
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.critical('postpaidSmsParsing :: publishFailedRecordInKafka', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(record), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:FAILED_SMS_PARSING_REGEX_BACKUP", 
                        "OPERATOR:" + _.get(record, 'operator', null),
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.log('postpaidSmsParsing :: publishFailedRecordInKafka', 'Message published successfully in Kafka', ' on topic FAILED_SMS_PARSING_REGEX_BACKUP');
                }
                return cb();
            }, [200, 800]);
        }
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source           = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type        = user_type;
        recordForAnalytics.customer_id      = _.get(record, 'cId', null);
        recordForAnalytics.service          = (self.getServiceCategoryFromRecord(record)).toLocaleLowerCase();
        recordForAnalytics.recharge_number  = self.reduceLengthOfRechargeNumber(recordForAnalytics.service, record);
        recordForAnalytics.operator         = _.get(record, 'smsOperator', null);
        recordForAnalytics.due_amount       = _.get(record, 'dueAmt', null);
        recordForAnalytics.additional_info  = null;
        recordForAnalytics.sms_id           = _.get(record, 'msg_id', null);
        recordForAnalytics.paytype          =  "postpaid";
        recordForAnalytics.updated_at       = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id        = _.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time    = self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null), recordForAnalytics);
        recordForAnalytics.sms_class_id     = _.get(record, 'level_2_category', null);
        recordForAnalytics.due_date         = _.get(record, 'dueDate', utility.getFilteredDate(_.get(record, 'telecom_details.due_date', _.get(record, 'dueDate', null))).value);
        recordForAnalytics.bill_date        = _.get(record, 'billDate', utility.getFilteredDate(_.get(record, 'telecom_details.bill_date', _.get(record, 'billDate',null))).value || MOMENT(record.smsDateTime));
        recordForAnalytics.bill_fetch_date  = _.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    reduceLengthOfRechargeNumber(category, record) {
        let self = this;
        let rechargeNumber = _.get(record, 'rechargeNumber', null),
            isSmsReceiverPresent = false;
        if (!rechargeNumber && category == 'mobile') {
            rechargeNumber = _.get(record, 'telecom_details.mobile_number', null);
            if(!rechargeNumber || rechargeNumber == 'null') rechargeNumber = _.get(record, 'smsReceiver', null);
            isSmsReceiverPresent = true;
        }
        if(rechargeNumber && category == "mobile") {
            rechargeNumber = rechargeNumber.toString();
            if (rechargeNumber.length >= 10) {
                rechargeNumber = rechargeNumber.slice(-10)
                return rechargeNumber;
            }
        }
        return rechargeNumber;
    }

    createRecordForAnalytics1(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source           = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type        = user_type;
        recordForAnalytics.customer_id      = _.get(record, 'customerId', null);
        recordForAnalytics.service          = (_.get(record, 'category', null)).toLowerCase();
        recordForAnalytics.recharge_number  = self.reduceLengthOfRechargeNumber(recordForAnalytics.service, record);
        recordForAnalytics.operator         = _.get(record, 'operator', null);
        recordForAnalytics.due_amount       = _.get(record, 'amount', null);
        recordForAnalytics.additional_info  = null;
        recordForAnalytics.sms_id           =_.get(record, 'msgId', null);
        recordForAnalytics.paytype          =_.get(record, 'paytype', null);
        recordForAnalytics.updated_at       =_.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id        =_.get(record, 'sender_id', null);
        recordForAnalytics.sms_date_time    =self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null), recordForAnalytics);
        recordForAnalytics.sms_class_id     =_.get(record, 'dwh_classId', null);
        recordForAnalytics.due_date         =_.get(record, 'dueDate', null);
        recordForAnalytics.bill_date        =_.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date  =_.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    async saveAndPublishBillFetchAnalyticsData(record, error, cb, cbParam) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record,error);
        } catch(e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        if(cbParam) {
            cb(error,cbParam);
        } else {
            cb(error);
        }
    }

    async saveAndPublishBillFetchAnalyticsDataWithoutError(record, error, cb) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record,error);
        } catch(e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        cb();
    }

    getEpochminiToTimestampString(time, recordForAnalytics) {
        
        if(_.get(recordForAnalytics, "service", null) == 'mobile'){
            return time;
        }
        else {
            if (typeof time == "number" || (Number(time) != NaN && Number(time) > 0)) {
                return MOMENT(Number(time)).format('YYYY-MM-DD HH:mm:ss');
            }
            return MOMENT().format('YYYY-MM-DD HH:mm:ss')
        }
    }

    saveForAnalyticsInCassandraAndKafka(record) {
        let self = this;
        if(self.saveForAnalyticsInCassandraDbAndKafka){   
            let classId = _.get(record, 'dwh_classId', null);
            if(!classId) _.get(record, 'level_2_category', null);

            if(classId) {
                classId = (typeof classId == "string" ) ? classId : `${classId}`;
                let allowedClassIdsForAnalytics = _.get(self.config, ['DYNAMIC_CONFIG', 'SMSPARSING', 'AllowedDwhClassIdsForAnalytics','classIds'],["1","5","6","8","11"]);
                if(allowedClassIdsForAnalytics.includes(classId)) return true;
            }
        }
        return false;
    }

    getPublishFlags(processedRecord) {
        const isPostpaidRecordExist = _.get(processedRecord, 'isRecordExist', false);
        const isSameCustomerId = _.get(processedRecord, 'recordFoundOfSameCustId', false);
        const isSameCustomerIdPrepaid = _.get(processedRecord, 'recordFoundOfSameCustIdPrepaid', false); 
        const isPrepaidRecordExist = _.get(processedRecord, 'isPrepaidDataFound', false);
        const amount = _.get(processedRecord, 'amount', null);
    
        const publishInNonRu = (!isPostpaidRecordExist && !isPrepaidRecordExist) || 
                              (isPostpaidRecordExist && !isSameCustomerId) || 
                              (isPrepaidRecordExist && !isSameCustomerIdPrepaid);
    
        const publishInNotification = isPostpaidRecordExist &&
            amount < this.minPrepaidBalanceCheck;
    
        return {
            publishInNonRu,
            publishInNotification
        };
    }

    checkIfOldSMSRejectionCase(processedRecord){
        let self = this;
        let smsDateTime = _.get(processedRecord, 'smsDateTime', null);
        let oldSMSRejectAllowedDays = _.get(self.config, ['DYNAMIC_CONFIG', 'OLD_SMS_REJECTION_CONFIG', _.get(processedRecord, 'category', 'N.A'), 'DAY_MARGIN'], null);

        if(smsDateTime && oldSMSRejectAllowedDays != null && MOMENT().startOf('day').diff(MOMENT(smsDateTime).startOf('day'), 'days') > oldSMSRejectAllowedDays && processedRecord.dueDate == null){
            return true;
        }
        return false;
    }

}
export default postpaidSmsParsing;