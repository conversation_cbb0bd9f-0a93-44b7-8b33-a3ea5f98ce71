import _ from 'lodash'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer'
import VA<PERSON><PERSON><PERSON>OR from 'validator'
import <PERSON>Y<PERSON> from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import CATALOGVERT<PERSON><PERSON><PERSON>CHARGE from '../models/catalogVerticalRecharge';
import PG from '../lib/pg'
import DigitalCatalog from '../lib/digitalReminderConfig'
import RemindableUsersLibrary from '../lib/remindableUser'
import { Kafka, CompressionTypes, CompressionCodecs } from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import InternalCustIdNonRUFlowTagger from '../lib/InternalCustIdNonRUFlowTagger'
CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

import Q from 'q'

class fasTagSmsParsing{
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.paymentGatewayUtils = new PG(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.commonLib = new utility.commonLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.PIDMAPPING = _.get(this.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP'], {});
        this.allowedCustIDListforSMSParsingLowBalance = _.get(this.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'ALLOWED_SMS_PARSING_LOW_BALANCE','CUST_ID'], []);
        this.lowBalanceThreshold = _.get(this.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'LOW_BALANCE_THRESHOLD_MAPPING', 'LOW_BALANCE_THRESHOLD'], '500');
        this.RUreadsKafkaTime =  new Date().getTime();
        this.CVR_DATA = _.get(this.config, 'CVR_DATA', null);
        this.grey_config = _.get(this.config,[ 'DYNAMIC_CONFIG', 'GREYSCALE_CONFIG']);
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        setInterval(() => {
            this.L.log('SmsParsingFasTag :: Consumer is running..!!');
            this.initializeVariable();
        }, 600000);
    }

    initializeVariable() {
        let self = this;
        this.PIDMAPPING = _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP'], {});
        this.grey_config = _.get(self.config,[ 'DYNAMIC_CONFIG', 'GREYSCALE_CONFIG']);
        this.allowedCustIDListforSMSParsingLowBalance = _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'ALLOWED_SMS_PARSING_LOW_BALANCE','CUST_ID'], []);
        this.CVR_DATA = _.get(self.config, 'CVR_DATA', null);
        this.lowBalanceThreshold = _.get(this.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'LOW_BALANCE_THRESHOLD_MAPPING', 'LOW_BALANCE_THRESHOLD'], '500');

    }

    start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SmsParsingFasTag :: start', 'Unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('SmsParsingFasTag :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                try{
                    self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                        "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                    });
                    self.L.log("non paytm consumer initialised");
                    self.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                        if(error){
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_FATE_TAG", 'STATUS:ERROR','TYPE:NON_PAYTM_RECORDS_PUBLISHER','SOURCE:MAIN_FLOW']);
                        }
                        return next(error)
                    });
                }
                catch(err) {
                    self.L.error('SmsParsingFasTag :: initializeKafkaConsumer', 'Error occured while initializing kafka producer : ', err);
                    next(err);
                }
            },
            next => {
                try {
                    self.Kafka = new Kafka({
                        "brokers": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_FASTAG.HOSTS').split(','),
                        "clientId": `smsParsingFastag-consumer_${OS.hostname()}_${process.pid}`,
                    });
                    self.consumer = self.Kafka.consumer({
                        groupId: 'smsParsingFastag-consumer'
                    });
                    self.L.log("SmsParsingFasTag :: initializeKafkaConsumer", "Connecting to kafka consumer");
                    
                    self.consumer.connect()
                    .then(() => {
                        let topic = _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_FASTAG.SMS_PARSING_FASTAG_TOPIC', null);
                        return self.consumer.subscribe({
                            "topic": topic,
                            "fromBeginning": true
                        })
                    })
                    .then(() => {
                        self.consumer.run({
                            eachBatchAutoResolve: false,
                            eachBatch: async ({
                                batch,
                                resolveOffset,
                                heartbeat,
                                commitOffsetsIfNecessary,
                                uncommittedOffsets,
                                isRunning,
                                isStale
                            }) => {
                                let consumerHeartbeat = setInterval(function() {
                                    heartbeat();
                                    self.L.log('SmsParsingFasTag :: Consumer sent heartbeat...');
                                }, 500);
                                try {
                                    await new Promise((resolve, reject) => {
                                        self.execSteps(batch.messages, resolveOffset, batch.topic, batch.partition, resolve);
                                    });
                                } catch (err) {
                                    self.L.error('SmsParsingFasTag :: processNotifications', 'Error occured while processing data : ', err);
                                }
                                clearInterval(consumerHeartbeat);
                                self.L.log('SmsParsingFasTag :: setInterval(consumerHeartbeat) cleared...');
                            },
                        })
                    }).catch(err => {
                        self.L.error('SmsParsingFasTag :: initializeKafkaConsumer', 'Error occured while consuming data : ', err);
                        next(err);
                    });
                } catch(err) {
                    self.L.error('SmsParsingFasTag :: initializeKafkaConsumer', 'Error occured while initializing kafka consumer : ', err);
                    next(err)
                }
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records, resolveOffset, topic, partition, cb) {
        let self = this,
            chunkSize = 50,
            startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;
        self.RUreadsKafkaTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.consumer.pause([{topic, partitions:[partition]}]);
        } else {
            self.L.critical('execSteps :: ', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} SMS Parsing Fastag data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:SMSPFASTAG_TRAFFIC']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("SmsParsingFasTag", records, topic, partition);

                // commit message offset
                resolveOffset(lastMessage.offset);
                self.L.log('execSteps :: ', 'Commit success for offset:', _.get(lastMessage, 'offset') + ', topic: ' + topic + ', partition: ' + partition + ', timestamp: ' + _.get(lastMessage, 'timestamp'));
                
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps :: ', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:SmsParsingFastag", "TIME_TAKEN:" + executionTime]);

                setTimeout(function () {
                    // resume partition
                    self.consumer.resume([{topic, partitions:[partition]}]);

                    cb();
                }, self.kafkaBatchDelay);
            }
        );
    }

    processBatch(records, done) {
        let self = this,
         currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer+1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                return done()
            }
        );
    }

    processData(record, done) {
        let self = this;

        try {
            record = JSON.parse(_.get(record, 'value', null));
            if(!record.data){
                self.L.critical('processData', `Invalid Kafka record received. data key is missing`, record);
                return done();
            }
        } catch (error) {
            if (error) {
                self.L.critical('processData', `Invalid Kafka record received`, record);
                
            }
            return done();
        }
        ASYNC.map(
            record.data,
            (smsData, next) => {
                self.processRecords(() => {
                    return next();
                }, smsData);
            },
            err => {
                done();
            }
        )
    }

    processRecords(done,record) {
        let self = this;
        self.timestamps = {};
    
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:SMSP_FASTAG_TRAFFIC_DWH', 
            'STATUS:TRAFFIC', 
            `APP_VERSION:${_.get(record,'appVersion', '')}`,
            `APP_COUNT:${_.get(record,'appCount', null)}`
        ]);
        try {
            ASYNC.waterfall([
                next => {
                    self.validateAndProcessRecord(record, function(error, resp){
                        if (error) {
                            self.L.error(`processRecords`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:INVALID_RECORD_SMSP_FASTAG', 
                                'STATUS:VALIDATION_FAILURE', 
                                'TYPE:'+ error, 
                                `CLASSID:${_.get(resp ,"smsClass",'')}`,
                                `MODELID:${_.get(resp ,"modelVersion",'')}`,
                                `APP_VERSION:${_.get(record,'data_appVersion', '')}`,
                                `APP_COUNT:${_.get(record,'data_appCount', null)}`
                            ]);
                            return next(error);
                        }
                        else {
                            self.L.log(`Processing ${JSON.stringify(record)} record for`, resp.debugKey);
                            return next(null,resp);
                        }
                    });
                },
                (processedRecord, next) => {
                    let oldPid = self.fetchPIDFromConfig(processedRecord.operator, processedRecord);
                    
                    let productId = self.activePidLib.getActivePID(oldPid);
                    let productInfo = _.get(self.CVR_DATA,  [productId], null);
                    self.L.log('processRecords', `Product Info for pid ${productId} is ${JSON.stringify(productInfo)}`);
                    if(productInfo){
                            return next(null, processedRecord, productInfo);
                    }
                    else{
                        self.L.log(`No CVR data found for pid ${productId}, Stoping process ${JSON.stringify(record)} record for`, processedRecord.debugKey);
                        return next(null, processedRecord, productInfo);
                    }
                },
                (processedRecord, productInfo, next) => {
                    if (productInfo) {
                        self.L.log('processRecords', 'Checking for low balance data processing', 'debugKey:', processedRecord.debugKey);
                        let GREY_CONFIG = Number(_.get(this.grey_config, ['FASTAG_LOW_BALANCE', "PERCENTAGE"], null));
                        let customerId = processedRecord.customerId;
                        self.L.log('processRecords', `Checking for low balance data processing for customerId:${customerId} GREY_CONFIG:${GREY_CONFIG}`);
                        self.L.log('processRecords', `Allowed customer list for low balance data processing ${self.allowedCustIDListforSMSParsingLowBalance}`);
                        if ((GREY_CONFIG && GREY_CONFIG > customerId % 100) || (self.allowedCustIDListforSMSParsingLowBalance.includes(customerId))) {
                            self.L.log(`processRecords`, `Record having debug key :: customerId:${_.get(processedRecord, 'customerId', null)} is allowed for processing for low data balance`);
                            self.updateCassandra(processedRecord, productInfo, function (error, resp) {
                                return next(null, processedRecord);
                            })
                        }
                        else {
                            self.L.log('processRecords :: Record skipped to be published in cassandra as falling outside grey config', processedRecord.debugKey);
                            return next(null, processedRecord);
                        }
                    }
                    else {
                        return next(null, processedRecord);
                    }
                },
                (processedRecord,next) => {
                    if (_.get(processedRecord, 'isLowBalance', false) === false){
                        self.L.log('processRecords', `skipping publish CT events as record is not a low balance record, ${JSON.stringify(processedRecord)}`);
                        return next(`Records ${JSON.stringify(record)} is not a low balance record`);
                    }
                    self.publishCtAndPFCCEvents((error) => {
                        return next(error);
                    }, processedRecord, record);
                }
            ], function (error) {
                if(error) {
                    self.L.error('processRecords',`Exception occured Error Msg:: ${error} for record::${JSON.stringify(record)}`);
                } else {
                    self.L.log(`processRecords`,`Record processed having debug key :: customerId:${_.get(record, 'cId', null)}`);
                }
                return done(error);
            });
        } catch(err) {
            self.L.critical(`processRecords`,`Exception occured for record:${record}`,err)
            return done(err);
        }
    }

    fetchPIDFromConfig(bank_name, processedRecord){
        let self = this;

        let product_id;
        if(bank_name == null){
            product_id = self.PIDMAPPING['DUMMY_BANK'];
        }
        else if(self.PIDMAPPING[bank_name]){
            product_id = self.PIDMAPPING[bank_name];
        }
        else{
            product_id = self.PIDMAPPING['DUMMY_BANK'];
        }
        self.L.log("fetchPIDFromConfig :: pid:", product_id," picked for bankName: ", bank_name, " DebugKey: ", processedRecord.debugKey);
        return product_id;
    }

    validateAndProcessRecord(record,cb) {
        let self = this;

        //console.log("printing the record :: ",record);;
        
        if (!record) 
            return ['Invalid record', record];

        try {
            ASYNC.waterfall([
                next => {
                    let dateFormat = 'YYYY-MM-DD',
                        fasTagFeatures = _.get(record,'fastag_features',null);
                    let processedRecord = {
                        customerId: (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
                        rechargeNumber: _.get(fasTagFeatures, 'vehicle_number') ? _.trim(_.get(fasTagFeatures, 'vehicle_number').toUpperCase()) : null,
                        operator: _.get(fasTagFeatures, 'bank_name', null),
                        billDate: _.get(record, 'smsDateTime', ''),
                        billFetchDate: _.get(record, 'deviceDateTime', ''),
                        service: "fastag recharge",
                        smsClass: _.get(fasTagFeatures,'fastag_model_version','v1') =='v2.0' ?_.get(record, 'level_2_category', '') : _.get(record, 'fastag_class', ''),
                        smsExtra: _.get(record, 'newUser', ''),
                        smsSenderID: _.get(record, 'smsSenderID', ''),
                        modelVersion : _.get(fasTagFeatures,'fastag_model_version','v1'),
                        amount: isNaN(parseFloat(_.get(fasTagFeatures, 'balance_amount', _.get(fasTagFeatures, 'balance_recharge',null)))) ? null :utility.getFilteredAmount(_.get(fasTagFeatures, 'balance_amount', _.get(fasTagFeatures, 'balance_recharge',null)))
                    };

                    if(_.get(record, 'isRuSmsParsing', false)==true){
                        _.set(processedRecord, 'isRuSmsParsing', true)
                    }

                    _.set(processedRecord, 'isLowBalance', self.checkIfFastagBalanceisLow(processedRecord))

                    if(self.checkIfFastagBalanceisLow(processedRecord)){

                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_PREPAID",
                            `SOURCE:PS_PARSING`,
                            "SERVICE:fastag recharge",
                            "TYPE:COUNT",
                            `CLASSID:${_.get(processedRecord, 'smsClass', '')}`,
                            `MODELID:${_.get(processedRecord, 'modelVersion', '')}`,
                            `ORIGIN:SMS_PARSING_REALTIME_PREPAID`,
                            `PAYLOAD_TYPE:LOW_BALANCE`
                        ]);
                    }else{
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_PREPAID",
                            `SOURCE:PS_PARSING`,
                            "SERVICE:fastag recharge",
                            "TYPE:COUNT",
                            `CLASSID:${_.get(processedRecord, 'smsClass', '')}`,
                            `MODELID:${_.get(processedRecord, 'modelVersion', '')}`,
                            `ORIGIN:SMS_PARSING_REALTIME_PREPAID`,
                            `PAYLOAD_TYPE:NON_LOW_BALANCE`
                        ]);
                    }
                    let mandatoryParams = ['customerId','smsClass'];
                    let invalidParams = [];
                    mandatoryParams.forEach(function (key) {
                        if (!processedRecord[key]) invalidParams.push(key);
                    });
                    processedRecord.debugKey = `smsSenderID:${processedRecord.smsSenderID}_custId:${processedRecord.customerId}_rechargeNumber:${processedRecord.rechargeNumber}`;
                    if(invalidParams.length > 0){
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_PREPAID",
                            `SOURCE:PS_PARSING`,
                            "SERVICE:fastag recharge",
                            "TYPE:MANDATORY_PARAMS_MISSING",
                            `CLASSID:${_.get(processedRecord, 'smsClass', '')}`,
                            `MODELID:${_.get(processedRecord, 'modelVersion', '')}`,
                            `ORIGIN:SMS_PARSING_REALTIME_PREPAID`
                        ]);
                    }
                    if (invalidParams.length > 0)
                        return next (`Mandatory Params ${invalidParams} is Missing / Invalid`, record);
                    // else if(!self.isValidClassForFastagLowBalance(processedRecord))
                    //     return next(`Records ${record} smsClass is not 5 for model v1 and 3 for model v2.0`)
                    else 
                        return next(null, processedRecord);
                }
            ], function (error,processedRecord) {
                if(error) {
                    return cb(error);    
                } else {
                    return cb(null,processedRecord);    
                }
            });
        } catch(err) {
            self.L.critical(`validateAndProcessRecord`,`Exception occured for record:${JSON.stringify(record)}`,err)
            return cb(err);
        }
    }

    async updateCassandra(processedRecord, productInfo, callback) {
        let self = this;
        productInfo = JSON.parse(JSON.stringify(productInfo));
        let updateNonPaytm = true;
        // if (self.isValidClassForFastagProcessing(processedRecord)) {
        //     self.L.log("updateCassandra :: Data is eligible to be published in non paytm.", processedRecord.debugKey);
        //     updateNonPaytm = true;
        // }
        let operator, rechargeNumber;

        if(self.validateRechargeNumber(_.get(processedRecord, "rechargeNumber"))){
            rechargeNumber = _.get(processedRecord, "rechargeNumber");
        }else if (self.checkIfFastagBalanceisLow(processedRecord)){
            rechargeNumber = `default_${processedRecord.customerId}`;
            self.L.log("updateCassandra :: Default recharge number added for debugKey: ", processedRecord.debugKey);
        }else{
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_PREPAID",
                'STATUS:ERROR',
                `SOURCE:PS_PARSING`,
                "SERVICE:fastag recharge",
                "TYPE:INVALID_VRN",
                `CLASSID:${_.get(processedRecord, 'smsClass', null)}`,
                `MODELID:${_.get(processedRecord, 'modelVersion', null)}`,
                `ORIGIN:SMS_PARSING_REALTIME_PREPAID`,
                `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
            ]);
            self.L.log("updateCassandra :: Invalid data for Non Low balance, payload ", JSON.stringify(processedRecord));
            updateNonPaytm = false;
        }
        if(updateNonPaytm == true){
            let billDateRecord = _.get(processedRecord, 'billDate');
           
            let smsDateTime = MOMENT();
            if (billDateRecord) {
                smsDateTime = MOMENT(billDateRecord);
                if (!smsDateTime.isValid()) {
                    smsDateTime = MOMENT();
                }
            }
            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId, // customer Id 
                rechargeNumber: rechargeNumber, // vehicle number
                productId: _.get(productInfo, 'product_id', null),
                operator: _.get(productInfo, "operator", null).toLowerCase(),
                amount: _.get(processedRecord, 'amount', null),
                bill_fetch_date: null,
                paytype: _.get(productInfo,"paytype", "prepaid").toLowerCase(),
                service: _.get(productInfo, "service", "fastag recharge").toLowerCase(),
                circle:  _.get(productInfo, "circle", null).toLowerCase(),
                customer_mobile: null,
                customer_email: null,
                status: self.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM, 
                userData: null, 
                billDate:  (_.get(processedRecord, 'isLowBalance', false) == true) ? smsDateTime : MOMENT(),
                notificationStatus: 1, 
                dueDate: null,
                customerOtherInfo: JSON.stringify(processedRecord),
                extra:  self.generateExtraForLowBalance(processedRecord),//JSON.stringify({"type": "LOW_BALANCE", "low_balance_date": MOMENT(new Date()).format("YYYY-MM-DD HH:mm:ss")}),
                planBucket: "",
                dbEvent:  "upsertWithRead",
                dwhClassId: _.get(processedRecord, 'smsClass', null),
                rtspClassId: null,
                source: 'SMS_PARSING_REALTIME_PREPAID' ,
                dwhModelId: _.get(processedRecord, 'modelVersion', null),
                isLowBalance: _.get(processedRecord, 'isLowBalance', false)
            }

            let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);

            self.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID",
                        'STATUS:ERROR',
                        `SOURCE:PS_PARSING`,
                        "SERVICE:fastag recharge",
                        "TYPE:NON_PAYTM_EVENTS",
                        `CLASSID:${_.get(processedRecord, 'smsClass', null)}`,
                        `MODELID:${_.get(processedRecord, 'modelVersion', null)}`,
                        `ORIGIN:SMS_PARSING_REALTIME_PREPAID`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                    ]);
                    self.L.critical('SMS_PARSING_PREPAID:PS_PARSING:nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID",
                        'STATUS:PUBLISHED',
                        "TYPE:NON_PAYTM_EVENTS",
                        `SOURCE:PS_PARSING`,
                        "SERVICE:fastag recharge",
                        `CLASSID:${_.get(processedRecord, 'smsClass', null)}`,
                        `MODELID:${_.get(processedRecord, 'modelVersion', null)}`,
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:SMS_PARSING_REALTIME_PREPAID`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`,
                    ]);
                    self.L.log('SMS_PARSING_PREPAID:PS_PARSING:nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_EVENTS', nonRuDataToPublish);
                }
                callback(error);
            })
        }
        else{
            callback(null);
        }


    }

    publishCtAndPFCCEvents(done, processedRecord, record) {
        let self = this;

        const customerId = _.get(processedRecord, 'customerId', '');
        const operator = _.get(processedRecord,'operator','');
        const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS_FASTAG', 'BILLGEN'], 'smsparsedFastagRecharge')
        const dbDebugKey = `rec:${processedRecord.rechargeNumber}::cust:${customerId}::op:${processedRecord.operator}::service:${processedRecord.service}`;

        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, processedRecord);
            },
            next => {

                let mappedData = self.reminderUtils.createCTPipelinePayload(processedRecord, eventName, dbDebugKey);
                
                //console.log("printing the mappedData :: " + mappedData);
                
                let replicatedData = _.cloneDeep(mappedData);

                ASYNC.parallel([
                    function (cb) {
                            self.ctKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                                messages: JSON.stringify(replicatedData)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:SMS_PARSING", 
                                        'STATUS:ERROR', 
                                        "TYPE:CT_EVENTS_FASTAG",
                                        `APP_VERSION:${_.get(processedRecord,'data_appVersion', '')}`,
                                        `APP_COUNT:${_.get(processedRecord,'data_appCount', null)}`,
                                        "OPERATOR:" + operator
                                    ]);
                                    self.L.critical('publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(replicatedData), error);
                                } else {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:SMS_PARSING", 
                                        'STATUS:PUBLISHED', 
                                        "TYPE:CT_EVENTS_FASTAG",
                                        `APP_VERSION:${_.get(processedRecord,'data_appVersion', '')}`,
                                        `APP_COUNT:${_.get(processedRecord,'data_appCount', null)}`,
                                        "OPERATOR:" + operator
                                    ]);
                                    self.L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(replicatedData));
                                }
                                cb(error);
                            }, [200, 800]);
                    },
                ], function done(err) {
                    return next(err);
                });
            }
        ], error => {
            if(error) {
                self.L.error('publishCtAndPFCCEvents',`Exception occured Error Msg:: ${error} for record::${JSON.stringify(processedRecord)} debugKey::`, dbDebugKey);
            } else {
                self.L.log(`publishCtAndPFCCEvents`,`Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }

    isValidClassForFastagLowBalance(processedRecord){
            let self = this;
            let classVer = processedRecord.smsClass + processedRecord.modelVersion
            if(_.get(self.config, ['DYNAMIC_CONFIG','FASTAG_CONFIG','FASTAG_CLASS_CONFIG','CLASS_LIST'], ['3v2.0','5v1']).includes(classVer)){
                return true;
            }
            return false;

        }

        generateExtraForLowBalance(processedRecord){
            let self = this;
            if (self.checkIfFastagBalanceisLow(processedRecord)) {
                let billDate = _.get(processedRecord, 'billDate');
           
                let smsDateTime = null;
                if (billDate) {
                    const momentDate = MOMENT(billDate);
                    if (momentDate.isValid()) {
                        smsDateTime = momentDate.valueOf(); // Gets timestamp in milliseconds
                    } else {
                        self.L.error('generateExtraForLowBalance', `Invalid date format received: ${billDate}`);
                    }
                }

                return JSON.stringify({
                        "type": "LOW_BALANCE", 
                        "low_balance_date": MOMENT(new Date()).format("YYYY-MM-DD HH:mm:ss"),
                        "smsDateTime": smsDateTime
                    });
            }
        }

    validateRechargeNumber(vehicle_number){
            let self = this;
            self.L.log("validateRechargeNumber :: validating recharge number ", vehicle_number);

            let configArr = _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'VRN_SIZE_MAPPING', 'VRN_SIZE_LIST'], [9,10,11]);  //get vrn length list here from config
            let regex = _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'VRN_SIZE_MAPPING', 'VRN_REGEX'], "^[a-zA-Z0-9]{8,11}$");
            let re = new RegExp(regex);
            if(vehicle_number && configArr.includes(vehicle_number.length) && re.test(vehicle_number) ){
                return true;
            }else{
                return false;
            }
        }

    checkIfFastagBalanceisLow(processedRecord){
            let self = this;
            let amount = _.get(processedRecord, 'amount', null);
            if ((amount != null && typeof amount === 'number' && Number(amount) < Number(self.lowBalanceThreshold)) || self.isValidClassForFastagLowBalance(processedRecord)) {
                return true;
            }
            return false;
        }

}
export default fasTagSmsParsing;