import _ from 'lodash';
class InternalCustIdNonRUFlowTagger{
    constructor(options) {
        this.options = options;
        this.internalCustIdMap = options.internalCustIdMap;
    }

    async mapInternalCustIdToNonRUFlow(record) {
        const rechargeNumber = _.get(record, 'recharge_number', _.get(record, 'rechargeNumber', _.get(record, 'rechargeNumber', '')));
        const service = _.get(record, 'service', null);
        const paytype = _.get(record, 'paytype', null);
        const operator = _.get(record, 'operator', null);
        const key = rechargeNumber + "_" + service + "_" + paytype + "_" + operator;
        const internalCustIds = this.internalCustIdMap[key];
        console.log("🚀 ~ InternalCustIdNonRUFlowTagger ~ mapInternalCustIdToNonRUFlow ~ this.internalCustIdMap:", this.internalCustIdMap)
        let nonRuData = [];
        nonRuData.push(JSON.stringify(record));
        if (internalCustIds && internalCustIds.length > 0) {
            internalCustIds.map(internalCustId => {
                let clonedData = _.cloneDeep(record);
                clonedData.customerId = internalCustId;
                nonRuData.push(JSON.stringify(clonedData));
            });
        }
        return nonRuData;
    }
}

export default InternalCustIdNonRUFlowTagger;