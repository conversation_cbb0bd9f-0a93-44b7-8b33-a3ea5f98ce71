import Q from 'q'
import _ from 'lodash'
import Bills from '../models/bills'

class LoadInternalCustIdLib {

    constructor(options) {
        this.refreshInterval = 10 * 1000;
        this.L = options.L;
        this.config = options.config;
        this.bills = new Bills(options);    
        this.options = options;
    }

    load() {
        let self = this;
        let deferred = Q.defer();
        self.getInternalCustId(function (err, internalCustIdMap) {
            if (err) {
                deferred.reject(err);
            }
            self.options.internalCustIdMap = internalCustIdMap;
            setInterval(function () {
                self.getInternalCustId(function (err, internalCustIdMap) {
                    if (err) {
                        self.L.critical(`Unable to parse dynamic config: ${err}`);
                    } else {
                        self.options.internalCustIdMap = internalCustIdMap;
                    }
                });
            }, self.refreshInterval);
            deferred.resolve();
        });

        return deferred.promise;
    }

    getInternalCustId(cb) {
        let self = this;
        self.bills.getInternalCustId(function (err, data) {
            if (err) {
                self.L.error("Error in getting internal cust id", err);
                return cb(err);
            } else {
                self.L.log("Internal cust id fetched successfully", data);
                //map the custId to recharge_number and service and paytype and operator
                let internalCustIdMap = {};
                data.forEach(item => {
                    //recharge_number_service_paytype_operator vs customer_id
                    const key = item.recharge_number + "_" + item.service + "_" + item.paytype + "_" + item.operator;
                    if (!internalCustIdMap[key]) {
                        internalCustIdMap[key] = [];
                    }
                    internalCustIdMap[key].push(item.customer_id);
                });
                self.L.log("Internal cust id map", internalCustIdMap);
                return cb(null, internalCustIdMap);
            }
            return cb(null, data);
        });
    }
}

export default {
    LoadInternalCustIdLib
};