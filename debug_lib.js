// Debug script to check LoadInternalCustIdLib export
const LIB = require('./dist/lib/index.js').default;

console.log('LIB keys:', Object.keys(LIB));
console.log('LoadInternalCustIdLib:', LIB.LoadInternalCustIdLib);
console.log('LoadInternalCustIdLib type:', typeof LIB.LoadInternalCustIdLib);
console.log('LoadInternalCustIdLib constructor:', LIB.LoadInternalCustIdLib.constructor);

// Try to create an instance
try {
    const instance = new LIB.LoadInternalCustIdLib({});
    console.log('Instance created successfully:', instance);
} catch (error) {
    console.log('Error creating instance:', error.message);
}
